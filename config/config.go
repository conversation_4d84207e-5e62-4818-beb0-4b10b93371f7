package config

import (
	"io"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/fsnotify/fsnotify"
	"github.com/gin-gonic/gin"
	rotatelogs "github.com/lestrrat-go/file-rotatelogs"
	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"
)

type Config struct {
	Name string
}


// 读取配置
func (c *Config) InitConfig() error {
	if c.Name != "" {
		viper.SetConfigFile(c.Name)
	} else {
		viper.AddConfigPath("conf")
		viper.SetConfigName("config")
	}
	viper.SetConfigType("yaml")

	// 从环境变量中读取
	viper.AutomaticEnv()
	viper.SetEnvPrefix("web")
	viper.SetEnvKeyReplacer(strings.NewReplacer("_", "."))

	return viper.ReadInConfig()
}

// 监控配置改动
func (c *Config) WatchConfig(change chan int) {
	viper.WatchConfig()
	// TODO: 这个会触发两次, 考虑使用限流模式, 第一次是无效的
	// https://github.com/gohugoio/hugo/blob/master/watcher/batcher.go
	viper.OnConfigChange(func(e fsnotify.Event) {
		logrus.Infof("配置已经被改变: %s", e.Name)

		if viper.GetBool("server_auto_reload") {
			// 非常有可能读到空的
			if err := viper.ReadInConfig(); err != nil || viper.GetString("db.addr") == "" {
				if err == nil {
					logrus.Warnf("配置更新后读取失败: 未读到数据")
				} else {
					logrus.Warnf("配置更新后读取失败: %s", err)
				}

				return
			}
			change <- 1
		}
	})
}

// indexName es index name 时间分割
func IndexName() string {
	return viper.GetString("eslog.index") + "-" + time.Now().Local().Format("2006-01-02")
}

// 初始化日志
func (c *Config) InitLog() {
	// log.logrus_json
	if viper.GetBool("log.logrus_json") {
		logrus.SetFormatter(&logrus.JSONFormatter{
			TimestampFormat: "2006-01-02 15:04:05",
			// 按照字符排序的，无法自定义排序，@排前面
			FieldMap: logrus.FieldMap{
				logrus.FieldKeyTime:  "atime",
				logrus.FieldKeyLevel: "blevel",
				logrus.FieldKeyMsg:   "cmsg",
			},
		})
	}

	// log.logrus_level
	switch viper.GetString("log.logrus_level") {
	case "trace":
		logrus.SetLevel(logrus.TraceLevel)
	case "debug":
		logrus.SetLevel(logrus.DebugLevel)
	case "info":
		logrus.SetLevel(logrus.InfoLevel)
	case "warn":
		logrus.SetLevel(logrus.WarnLevel)
	case "error":
		logrus.SetLevel(logrus.ErrorLevel)
	}

	// log.logrus_file
	logrusFile := viper.GetString("log.logrus_file")
	logrusMaxAge := viper.GetInt("log.logrus_max_age")
	logrusRotationTime := viper.GetInt("log.logrus_rotation_time")
	err := os.MkdirAll(filepath.Dir(logrusFile), os.ModePerm)
	if err != nil {
		logrus.Errorf("logrus_file os.MkdirAll err %#v", err)
	}

	logrusFileList := strings.Split(logrusFile, "/")
	logruslinkName := logrusFileList[len(logrusFileList)-1]

	logrusWriter, logruserr := rotatelogs.New(
		logrusFile+"_%Y-%m-%d-%H-%M.log",
		rotatelogs.WithLinkName(logruslinkName),                                  // 生成软链，指向最新日志文件
		rotatelogs.WithMaxAge(time.Duration(logrusMaxAge)*time.Hour),             // 文件最大保存时间
		rotatelogs.WithRotationTime(time.Duration(logrusRotationTime)*time.Hour), // 日志切割时间间隔
		// WithMaxAge和WithRotationCount二者只能设置一个，
		// WithMaxAge设置文件清理前的最长保存时间，
		// WithRotationCount设置文件清理前最多保存的个数。
		// rotatelogs.WithMaxAge(time.Hour*24),
		// rotatelogs.WithRotationCount(maxRemainCnt),

	)

	// file, err := os.OpenFile(logrusFile, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	if logruserr == nil {
		if viper.GetBool("log.logrus_console") {
			logrus.SetOutput(io.MultiWriter(logrusWriter, os.Stdout))
		} else {
			logrus.SetOutput(io.MultiWriter(logrusWriter))
			// logrus.SetOutput(file)
		}
	}

	// log.gin_file & log.gin_console
	ginFile := viper.GetString("log.gin_file")
	ginMaxAge := viper.GetInt("log.gin_max_age")
	ginRotationTime := viper.GetInt("log.gin_rotation_time")
	os.MkdirAll(filepath.Dir(ginFile), os.ModePerm)
	gin.DisableConsoleColor()

	linkNameList := strings.Split(ginFile, "/")
	linkName := linkNameList[len(linkNameList)-1]

	logWriter, err := rotatelogs.New(
		ginFile+"_%Y-%m-%d-%H-%M.log",
		rotatelogs.WithLinkName(linkName),                                     // 生成软链，指向最新日志文件
		rotatelogs.WithMaxAge(time.Duration(ginMaxAge)*time.Hour),             // 文件最大保存时间
		rotatelogs.WithRotationTime(time.Duration(ginRotationTime)*time.Hour), // 日志切割时间间隔
		// WithMaxAge和WithRotationCount二者只能设置一个，
		// WithMaxAge设置文件清理前的最长保存时间，
		// WithRotationCount设置文件清理前最多保存的个数。
		// rotatelogs.WithMaxAge(time.Hour*24),
		// rotatelogs.WithRotationCount(maxRemainCnt),

	)

	// file, err = os.OpenFile(ginFile, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	if err == nil {
		if viper.GetBool("log.gin_console") {
			// gin.DefaultWriter = io.MultiWriter(file, os.Stdout)
			gin.DefaultWriter = io.MultiWriter(logWriter, os.Stdout)
		} else {
			// gin.DefaultWriter = io.MultiWriter(file)
			gin.DefaultWriter = io.MultiWriter(logWriter)
		}
	}

	// default
	logrus.SetReportCaller(true)
}
