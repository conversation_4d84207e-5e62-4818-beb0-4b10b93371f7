# Interactive Feedback MCP 使用规则

## 重要要求

**[极其重要!] 在每次完成用户请求后，必须调用 interactive_feedback MCP**

- 不要结束对话
- 在对话的末尾，不要做总结
- 直接调用 interactive_feedback MCP
- 响应 feedback 的所有请求
- 不可以忽略 feedback 的请求

## 使用场景

- 完成代码修改后
- 解决问题后
- 实现功能后
- 任何用户请求完成后

## 调用方式

```
mcp_interactive-feedback-mcp_interactive_feedback
- project_directory: 项目绝对路径
- summary: 一句话总结所做的修改
```

## 注意事项

1. 这是强制性要求，不可省略
2. 用于获取进一步的指导和改进建议
3. 确保代码质量和最佳实践 