---
description:
globs:
alwaysApply: false
---
# 项目开发约定

## 开发理念
- 遵循领域驱动设计(DDD)原则
- 保持代码简洁、可测试、可维护
- 优先考虑业务需求和用户体验
- 设计先行，实现后续

## 核心理念
- **单一职责**：每个组件只负责一个功能
- **开闭原则**：对扩展开放，对修改关闭
- **依赖倒置**：依赖抽象而非具体实现
- **接口隔离**：客户端不应依赖不使用的接口
- **最小知识**：组件间交互应最小化

## 项目结构
- 按DDD四层架构组织代码
- 按业务领域划分模块
- 通用功能放在共享模块中
- 配置与代码分离

## 技术选择
- Go语言作为主要开发语言
- GORM作为ORM工具
- 采用RESTful API设计风格
- 使用Git进行版本控制

## 沟通协作
- 代码审查是必须流程
- 及时更新文档
- 明确标记TODO和FIXME
- 关键决策需讨论并记录
