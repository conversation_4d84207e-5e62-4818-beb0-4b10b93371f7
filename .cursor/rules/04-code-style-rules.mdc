---
description: 
globs: 
alwaysApply: false
---
# 项目编码规范

## 命名约定
- 变量和函数使用驼峰命名法
- 结构体和接口首字母大写
- 包名使用全小写单词
- 常量使用全大写加下划线
- 文件名使用小写加下划线

## 代码组织
- 按DDD层次组织代码结构
- 相关功能放在同一个包中
- 单个文件不超过500行
- 公共函数放在包级别
- 单个函数不超过50行

## 错误处理
- 使用显式错误处理，不使用panic
- 函数返回值放在最后一个参数
- 使用有意义的错误信息
- 避免嵌套错误处理
- 使用错误包装传递上下文

## 注释规范
- 每个导出的函数和类型必须有文档注释
- 注释解释代码的"为什么"而不是"是什么"
- 使用完整句子描述函数和类型
- 复杂逻辑必须有注释说明
- TODO和FIXME注释必须包含作者和日期

## 版本控制
- 提交前进行代码审查
- 每次提交专注于单个功能
- 提交消息描述变更的内容和原因
- 避免提交未使用的代码
- 避免提交敏感信息
