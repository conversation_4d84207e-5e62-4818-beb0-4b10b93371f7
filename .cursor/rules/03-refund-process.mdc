---
description: 
globs: 
alwaysApply: false
---
# 退款业务流程规范

## 退款基本原则
- 退款必须保证数据一致性和完整性
- 退款金额不得超过原订单金额
- 退款必须创建反向的业务记录
- 第三方支付的退款必须跟踪处理结果

## 退款流程概要
1. **参数验证**：验证退款数据合法性
2. **业务验证**：确认商品可退、金额合理性
3. **数据准备**：收集相关的订单和支付记录
4. **数据创建**：生成退款订单和支付记录
5. **事务处理**：在事务中执行数据库操作
6. **异步处理**：处理第三方支付退款请求

## 退款优先级规则
现金退款或原路返回时，应按以下优先级分配退款金额：
1. 现金
2. 微信支付
3. 支付宝
4. 银行卡
5. 第三方支付
6. 会员卡

## 特殊处理规则
- 部分退款时，应先退未使用的商品
- 第三方支付退款失败时，应记录错误并提供人工处理方案
- 退款金额应精确到分，避免舍入误差
