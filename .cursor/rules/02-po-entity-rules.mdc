---
description: 
globs: 
alwaysApply: false
---
# 实体设计规范

## 通用属性规范
所有实体必须包含以下标准字段：

```go
Id           *string  `json:"id"`     // 唯一标识符
Ctime        *int64   `json:"ctime"`  // 创建时间
Utime        *int64   `json:"utime"`  // 更新时间
State        *int     `json:"state"`  // 状态
Version      *int     `json:"version"`// 版本
```

## 数据类型规范
- 时间值必须使用 `*int64` 类型表示
- 字符串类型必须使用 `*string` 类型
- 数值类型根据实际需求使用 `*int`, `*int64` 或 `*float64`
- 布尔值使用 `bool` 类型

## 领域对象分类
- **值对象(VO)**：不可变对象，通过所有属性值确定身份
- **实体(Entity)**：有唯一标识的对象，可变但身份不变
- **聚合(Aggregate)**：相关实体的集合，维护不变性规则
- **领域服务(Domain Service)**：处理跨实体的业务逻辑

## 实体创建规则
- 使用工厂方法创建复杂实体
- 使用构建器模式处理多参数实体创建
- 通过验证方法确保实体状态合法性

## 业务领域规则
- 退款必须遵循原支付方式的优先级顺序
- 订单状态变更必须通过指定的状态转换函数实现
- 价格计算必须考虑会员折扣和促销规则
