package _const

// 存酒相关的状态常量
const (
	// 商品状态常量定义
	StorageStateNormal    = 0 // 正常状态（已存）
	StorageStateDiscarded = 1 // 已报废
	StorageStateCancelled = 2 // 已撤销

	// 存酒单号前缀
	ProductStorageOrderPrefix   = "PS" // 存酒单号前缀
	ProductWithdrawOrderPrefix  = "PW" // 取酒单号前缀
	ProductDirectWithdrawPrefix = "PD" // 直接取酒单号前缀

	// 操作类型常量
	OperationTypeStorage    = "storage"     // 存酒
	OperationTypeStorageAdd = "storage_add" // 追加存酒
	OperationTypeWithdraw   = "withdraw"    // 取酒
	OperationTypeExtend     = "extend"      // 续存
	OperationTypeDiscard    = "discard"     // 报废
	OperationTypeCancel     = "cancel"      // 撤销

	// 操作名称常量
	OperationNameStorage    = "存酒"   // 存酒
	OperationNameStorageAdd = "追加存酒" // 追加存酒
	OperationNameWithdraw   = "取酒"   // 取酒
	OperationNameExtend     = "续存"   // 续存
	OperationNameDiscard    = "报废"   // 报废
	OperationNameCancel     = "撤销"   // 撤销

	// 状态代码常量
	StatusCodeNormal    = "normal"    // 正常
	StatusCodeWithdrawn = "withdrawn" // 已取完
	StatusCodePartial   = "partial"   // 部分支取
	StatusCodeDiscarded = "discarded" // 已报废
	StatusCodeCancelled = "cancelled" // 已撤销
	StatusCodeExtended  = "extended"  // 已续存

	// 状态名称常量
	StatusNameNormal    = "正常"   // 正常
	StatusNameWithdrawn = "已取完"  // 已取完
	StatusNamePartial   = "部分支取" // 部分支取
	StatusNameDiscarded = "已报废"  // 已报废
	StatusNameCancelled = "已撤销"  // 已撤销
	StatusNameExtended  = "已续存"  // 已续存

	DefaultRenewalDays                = 3  // 默认续期天数
	DefaultInitialStorageValidityDays = 30 // 默认首次存酒有效天数 (30天)
)
