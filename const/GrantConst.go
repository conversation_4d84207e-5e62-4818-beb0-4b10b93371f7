package _const

var (
	// 汇金云平台-公众号【服务号】
	GZH_CONF = map[string]string{
		"appid":        "wx91ef6ff49f601368",
		"appsecret":    "bafbf4d11d85983943b7d71f359433a2",
		"originid":     "gh_34f97605ba82",
		"granthost":    "https://medev-stage.ktvsky.com",
		"head":         "ozbab5",
		"unionid_head": "o6qE3t",
	}
	WXEvent_CONF = map[string]string{
		"token": "leishidianzi_merp_1234",
		"encodingAESKey": "OG9V95EOyxQeo8NDGtZDCnHESY7rvdmpO6DT9VxmmTo",
	}
	// 掌柜管理小程序-配置用
	MiniAppNew = map[string]string{
		"appid":        "wx99933985cf197656",
		"appsecret":    "34468a590eab2e5fb4e00e11c870e679",
		"originid":     "gh_c6feaca28701",
		"granthost":    "https://medev-stage.ktvsky.com",
		"head":         "ozbab5",
		"unionid_head": "o6qE3t",
	}
	// 汇金商户通-小程序-老板小程序-看报表
	MiniApp = map[string]string{
		"appid":        "wx7442db2d62a78ee3",
		"appsecret":    "aa89d2b2db2c37b1dd6a50cde3e1d893",
		"originid":     "gh_c147f7101a96",
		"granthost":    "https://ve.ktvsky.com",
		"head":         "ozbab5",
		"unionid_head": "o6qE3t",
	}
	// 雷石K歌助手-小程序-支付用的-小程序点餐用
	MiniAppPay = map[string]string{
		"appid":        "wx6ead01e45372ed39",
		"appsecret":    "d53d07e025a097759a774a15c7d596ca",
		"originid":     "gh_8b8367388d52",
		"granthost":    "https://ve.ktvsky.com",
		"head":         "ozbab5",
		"unionid_head": "o6qE3t",
	}
	// 支付宝-授权+支付
	ALICONF = map[string]string{
		"APPID":      "2021004151637224",
		"GATEWAY":    "https://openapi.alipay.com/gateway.do",
		"NOTIFY_URL": "http://kb.ktvsky.com/ali/callback",
		"granthost":  "https://ve.ktvsky.com",
		"PRIVATE_KEY": `*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`,
		// 应用公钥
		"ALI_PUB_KEY": `-----BEGIN RSA PRIVATE KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAgxNU/TobES123MpVQWfxtd9gf4kq0OJIWbNRiCzsLufTRFLZSLnlAI9GMvyl1veqjAFY4DvhtgUW+p3q/MBrk7yN/c44w2iwj7QVlPkFclHEOvENO8DRQyZTjJvre3JyIJimVpRF+AUGUuDdkEpZAYV99z/QmSX53VahVrN2TakDyrYcXC5Eq3hL4zbTjHC3s+n/D1dJuagv/2Yebtqen5kFmL5BatnlI8vyJPk5xTaUQcwiLHY82IIqZB4HSqza49y+YjK0I/gT1lwiMNL6bcaSlXk5HH9K+HJhClx5yTMXFCNP1o16XxKhFeIEsrLK/Ewxf0/gvK5y3Q+bndKS2wIDAQAB
-----END RSA PRIVATE KEY-----
`,
		// 支付宝公钥
		"ALI_2PUB_KEY": `-----BEGIN RSA PRIVATE KEY-----
		MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAkuC/2Ev5CEbAFHC9VqA89VSGhaH/eTm7wVhcQ/ReyhHqNh7zA8kniQvg9E43qyzjkw9aPhQwUIamBtFR3gjiuoY1bXSFzE2HYzHcO8OvQCtWjUDRGn1Qk7rklZZU5aiKrj+UF86FFWQCD5QlPYL6Sc85m8GtMwCcWaW1ZlT1jYJIv56ovFYB9Acjt1j3a45UPWSBGFEgAyFzian/KAshvlvUZDnVHUVORs80DmewI8ci0oqE/7kc/2uBqAteHLlqI2iMvCFU9anxKu4lubMLnyTWWGrISmUvaPJvONsqagPNaKH+8yGh/jXZufBdUYJhb5hSfpBJAva1v/TNnuestQIDAQAB
-----END RSA PRIVATE KEY-----
`,
	}
)
