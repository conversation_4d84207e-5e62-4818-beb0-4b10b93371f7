package _const

import "time"

var (

	// 雷石代理商id
	LESHUA_MERCHANT = map[string]string{
		"id":         "8685160",
		"wx_shop_id": "664235017",
	}
	// 交易进件密钥：A3A01B745BF84A79AE35E3CA49DA951E
	// 交易通知密钥：37D2EAEAD93D4277A98C94A5C9DE82F5
	LESHUA_CONF = map[string]string{
		"pay_url":      "https://paygate.leshuazf.com/cgi-bin/lepos_pay_gateway.cgi",
		"key":          "A3A01B745BF84A79AE35E3CA49DA951E",
		"key_callback": "37D2EAEAD93D4277A98C94A5C9DE82F5",
		"jj_host":      "https://saas-mch.leshuazf.com",
	}
	LESHUA_JINJIAN_UPDATE_RESPCODE = map[string]string{
		"000000": "直接成功",
		"000011": "自动审核",
		"000022": "人工审核",
		"000019": "修改失败",
	}
	LESHUA_ORDER_TIMEOUT                = 60 * 30         // 订单超时时间，单位：秒
	SHOP_CART_ORDER_TIMEOUT             = 60 * 5          // 购物车订单超时时间，单位：秒
	SHOP_CART_ROOM_LOCK_TIMEOUT         = 5 * time.Minute // 购物车订单超时时间，单位：秒
	SHOP_CART_ROOM_MINIAPP_PAY_LOCK_KEY = "lock_shop_cart_room_miniapp_pay_%d_%d"

	NarokCloudKtvSong = map[string]string{
		"appid":     "1250EB524F754BA7A4A0450C19E09F60",
		"appsecret": "8C8C3BA474004E78A706269D4D99E388",
	}
)
