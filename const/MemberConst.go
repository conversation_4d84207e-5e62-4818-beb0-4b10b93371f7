package _const

// 性别
const (
	MEMBER_GENDER_MALE   = "male"   // 男
	MEMBER_GENDER_FEMALE = "female" // 女
)

// 会员卡开卡状态
const (
	MEMBER_CARD_OPEN_STATUS_ACTIVE    = "active"    // 已开卡
	MEMBER_CARD_OPEN_STATUS_EXPIRED   = "expired"   // 已过期
	MEMBER_CARD_OPEN_STATUS_CANCELLED = "cancelled" // 已取消
	MEMBER_CARD_OPEN_STATUS_LOST      = "lost"      // 已挂失
	MEMBER_CARD_OPEN_STATUS_LOCKED    = "locked"    // 已锁定
)

// 会员卡类型，实体卡、电子卡
const (
	MEMBER_CARD_TYPE_PHYSICAL   = "physical_card"   // 实体卡
	MEMBER_CARD_TYPE_ELECTRONIC = "electronic_card" // 电子卡
)

// 周期频率
const (
	PERIOD_FREQUENCY_WEEK  = "week"
	PERIOD_FREQUENCY_MONTH = "month"
	PERIOD_FREQUENCY_YEAR  = "year"
)

// 会员卡，本金使用范围，不限、仅用于商品、仅用于包厢
const (
	MEMBER_CARD_BALANCE_USAGE_SCOPE_ALL     = "all"     // 不限
	MEMBER_CARD_BALANCE_USAGE_SCOPE_PRODUCT = "product" // 仅用于商品
	MEMBER_CARD_BALANCE_USAGE_SCOPE_ROOM    = "room"    // 仅用于包厢
)

// 卡消费时段类型，星期、日期
const (
	MEMBER_CARD_CONSUMPTION_TIME_TYPE_WEEK = "week" // 星期
	MEMBER_CARD_CONSUMPTION_TIME_TYPE_DATE = "date" // 日期
)

// 会员充值套餐类型
const (
	MEMBER_RECHARGE_PACKAGE_TYPE_FIXED   = "fixed"   // 固定金额
	MEMBER_RECHARGE_PACKAGE_TYPE_PERCENT = "percent" // 百分比
)

// 赠送金额类型，固定金额、充值金额百分比
const (
	MEMBER_RECHARGE_PACKAGE_GIVE_TYPE_FIXED   = "fixed"   // 固定金额
	MEMBER_RECHARGE_PACKAGE_GIVE_TYPE_PERCENT = "percent" // 充值金额百分比
)

// 会员套餐投放渠道：收银、线上自助、活动、移动点单
const (
	MEMBER_RECHARGE_PACKAGE_CHANNEL_CASHIER      = "cashier"      // 收银
	MEMBER_RECHARGE_PACKAGE_CHANNEL_SELF_SERVICE = "self_service" // 线上自助
	MEMBER_RECHARGE_PACKAGE_CHANNEL_ACTIVITY     = "activity"     // 活动
	MEMBER_RECHARGE_PACKAGE_CHANNEL_MOBILE_ORDER = "mobile_order" // 移动点单
)

// 会员卡结账类型约束
// 如果不设置，则默认本金、赠金均可结
const (
	MEMBER_CARD_SETTLEMENT_TYPE_BOTH         = "both"         // 本金、赠金可结
	MEMBER_CARD_SETTLEMENT_TYPE_ONLY_BALANCE = "only_balance" // 仅本金可结
	MEMBER_CARD_SETTLEMENT_TYPE_ONLY_GIFT    = "only_gift"    // 仅赠金可结
	MEMBER_CARD_SETTLEMENT_TYPE_NONE         = "none"         // 会员卡不可结
)

// 会员卡有效期类型，永久、相对有效期、固定有效期
const (
	MEMBER_CARD_VALIDITY_TYPE_PERMANENT = "permanent" // 永久
	MEMBER_CARD_VALIDITY_TYPE_RELATIVE  = "relative"  // 相对有效期
	MEMBER_CARD_VALIDITY_TYPE_FIXED     = "fixed"     // 固定有效期
)

// 会员卡有效期单位，年、月、日
const (
	MEMBER_CARD_VALIDITY_PERIOD_UNIT_YEAR  = "year"  // 年
	MEMBER_CARD_VALIDITY_PERIOD_UNIT_MONTH = "month" // 月
	MEMBER_CARD_VALIDITY_PERIOD_UNIT_DAY   = "day"   // 日
)

// 会员卡续卡周期单位，年、月、日
const (
	MEMBER_CARD_RENEWAL_PERIOD_UNIT_YEAR  = "year"  // 年
	MEMBER_CARD_RENEWAL_PERIOD_UNIT_MONTH = "month" // 月
	MEMBER_CARD_RENEWAL_PERIOD_UNIT_DAY   = "day"   // 日
)

// 会员卡操作类型
const (
	MEMBER_CARD_OPERATION_TYPE_OPEN_CARD          = "open_card"          // 开卡
	MEMBER_CARD_OPERATION_TYPE_RECHARGE           = "recharge"           // 充值
	MEMBER_CARD_OPERATION_TYPE_CONSUMPTION        = "consumption"        // 消费
	MEMBER_CARD_OPERATION_TYPE_TRANSFER           = "transfer"           // 转账
	MEMBER_CARD_OPERATION_TYPE_REFUND             = "refund"             // 退款
	MEMBER_CARD_OPERATION_TYPE_UPDATE_MEMBER_INFO = "update_member_info" // 修改会员信息
	MEMBER_CARD_OPERATION_TYPE_REISSUE            = "reissue"            // 补卡
	MEMBER_CARD_OPERATION_TYPE_RENEWAL            = "renewal"            // 续卡
	MEMBER_CARD_OPERATION_TYPE_DELETE             = "delete"             // 注销
	MEMBER_CARD_OPERATION_TYPE_FREEZE             = "freeze"             // 冻结
	MEMBER_CARD_OPERATION_TYPE_UNFREEZE           = "unfreeze"           // 解冻
	MEMBER_CARD_OPERATION_TYPE_LOST               = "lost"               // 挂失
	MEMBER_CARD_OPERATION_TYPE_UNLOST             = "unlost"             // 解挂
	MEMBER_CARD_OPERATION_TYPE_SET_PASSWORD       = "set_password"       // 设置密码
	MEMBER_CARD_OPERATION_TYPE_CHANGE_PASSWORD    = "change_password"    // 修改密码
)
