[{"po": "package po\n\n// StatisticsCategory 价格方案实体类\ntype StatisticsCategory struct {\n\tId           *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`           // ID\n\tName         *string `gorm:\"column:name;type:varchar(64);default:''\" json:\"name\"`                 // 统计分类名称\n\tVenueId      *string `gorm:\"column:venue_id;type:varchar(64);default:''\" json:\"venueId\"`         // 所属门店ID\n\tPricePlanIds *string `gorm:\"column:price_plan_ids;type:text;default:''\" json:\"pricePlanIds\"`     // 价格方案ids\n\tCtime        *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                       // 创建时间戳\n\tUtime        *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                       // 更新时间戳\n\tState        *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                       // 状态值\n\tVersion      *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`                   // 版本号\n}\n\n// TableName 设置表名\nfunc (StatisticsCategory) TableName() string {\n\treturn \"statistics_category\"\n}\n\nfunc (s StatisticsCategory) GetId() string {\n\treturn *s.Id\n}\n", "vo": "package vo\n\n// StatisticsCategoryVO 价格方案值对象\ntype StatisticsCategoryVO struct {\n\tId           string `json:\"id\"`           // ID\n\tName         string `json:\"name\"`         // 统计分类名称\n\tVenueId      string `json:\"venueId\"`      // 所属门店ID\n\tPricePlanIds string `json:\"pricePlanIds\"` // 价格方案ids\n\tCtime        int64  `json:\"ctime\"`        // 创建时间戳\n\tUtime        int64  `json:\"utime\"`        // 更新时间戳\n\tState        int    `json:\"state\"`        // 状态值\n\tVersion      int    `json:\"version\"`      // 版本号\n}\n", "req_add": "package req\n\n// AddStatisticsCategoryReqDto 创建价格方案请求DTO\ntype AddStatisticsCategoryReqDto struct {\n\tName         *string `json:\"name\"`         // 统计分类名称\n\tVenueId      *string `json:\"venueId\"`      // 所属门店ID\n\tPricePlanIds *string `json:\"pricePlanIds\"` // 价格方案ids\n}\n", "req_update": "package req\n\ntype UpdateStatisticsCategoryReqDto struct {\n\tId           *string `json:\"id\"`           // ID\n\tName         *string `json:\"name\"`         // 统计分类名称\n\tVenueId      *string `json:\"venueId\"`      // 所属门店ID\n\tPricePlanIds *string `json:\"pricePlanIds\"` // 价格方案ids\n}\n", "req_delete": "package req\n\ntype DeleteStatisticsCategoryReqDto struct {\n\tId *string `json:\"id\"` // ID\n}\n", "req_query": "package req\n\ntype QueryStatisticsCategoryReqDto struct {\n\tId           *string `json:\"id\"`           // ID\n\tName         *string `json:\"name\"`         // 统计分类名称\n\tVenueId      *string `json:\"venueId\"`      // 所属门店ID\n\tPricePlanIds *string `json:\"pricePlanIds\"` // 价格方案ids\n\tPageNum      *int    `json:\"pageNum\"`      // 页码\n\tPageSize     *int    `json:\"pageSize\"`     // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype StatisticsCategoryTransfer struct {\n}\n\nfunc (transfer *StatisticsCategoryTransfer) PoToVo(po po.StatisticsCategory) vo.StatisticsCategoryVO {\n\tvo := vo.StatisticsCategoryVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *StatisticsCategoryTransfer) VoToPo(vo vo.StatisticsCategoryVO) po.StatisticsCategory {\n\tpo := po.StatisticsCategory{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype StatisticsCategoryService struct {\n}\n\nfunc (service *StatisticsCategoryService) CreateStatisticsCategory(logCtx *gin.Context, statisticsCategory *po.StatisticsCategory) error {\n\treturn Save(statisticsCategory)\n}\n\nfunc (service *StatisticsCategoryService) UpdateStatisticsCategory(logCtx *gin.Context, statisticsCategory *po.StatisticsCategory) error {\n\treturn Update(statisticsCategory)\n}\n\nfunc (service *StatisticsCategoryService) DeleteStatisticsCategory(logCtx *gin.Context, id string) error {\n\treturn Delete(po.StatisticsCategory{Id: &id})\n}\n\nfunc (service *StatisticsCategoryService) FindStatisticsCategoryById(logCtx *gin.Context, id string) (statisticsCategory *po.StatisticsCategory, err error) {\n\tstatisticsCategory = &po.StatisticsCategory{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(statisticsCategory).Error\n\treturn\n}\n\nfunc (service *StatisticsCategoryService) FindAllStatisticsCategory(logCtx *gin.Context, reqDto *req.QueryStatisticsCategoryReqDto) (list *[]po.StatisticsCategory, err error) {\n\tdb := model.DBSlave.Self.Model(&po.StatisticsCategory{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name=?\", *reqDto.Name)\n\t}\n\tif reqDto.VenueId != nil && *reqDto.VenueId != \"\" {\n\t\tdb = db.Where(\"venue_id=?\", *reqDto.VenueId)\n\t}\n\tif reqDto.PricePlanIds != nil && *reqDto.PricePlanIds != \"\" {\n\t\tdb = db.Where(\"price_plan_ids=?\", *reqDto.PricePlanIds)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.StatisticsCategory{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *StatisticsCategoryService) FindAllStatisticsCategoryWithPagination(logCtx *gin.Context, reqDto *req.QueryStatisticsCategoryReqDto) (list *[]po.StatisticsCategory, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.StatisticsCategory{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name=?\", *reqDto.Name)\n\t}\n\tif reqDto.VenueId != nil && *reqDto.VenueId != \"\" {\n\t\tdb = db.Where(\"venue_id=?\", *reqDto.VenueId)\n\t}\n\tif reqDto.PricePlanIds != nil && *reqDto.PricePlanIds != \"\" {\n\t\tdb = db.Where(\"price_plan_ids=?\", *reqDto.PricePlanIds)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.StatisticsCategory{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype StatisticsCategoryController struct{}\n\nvar (\n\tstatisticsCategoryService  = impl.StatisticsCategoryService{}\n\tstatisticsCategoryTransfer = transfer.StatisticsCategoryTransfer{}\n)\n\n// @Summary 添加价格方案\n// @Description 添加价格方案\n// @Tags 价格方案\n// @Accept json\n// @Produce json\n// @Param body body req.AddStatisticsCategoryReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.StatisticsCategoryVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/statistics-category/add [post]\nfunc (controller *StatisticsCategoryController) AddStatisticsCategory(ctx *gin.Context) {\n\treqDto := req.AddStatisticsCategoryReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tstatisticsCategory := po.StatisticsCategory{}\n\tif reqDto.Name != nil {\n\t\tstatisticsCategory.Name = reqDto.Name\n\t}\n\tif reqDto.VenueId != nil {\n\t\tstatisticsCategory.VenueId = reqDto.VenueId\n\t}\n\tif reqDto.PricePlanIds != nil {\n\t\tstatisticsCategory.PricePlanIds = reqDto.PricePlanIds\n\t}\n\n\terr = statisticsCategoryService.CreateStatisticsCategory(ctx, &statisticsCategory)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, statisticsCategoryTransfer.PoToVo(statisticsCategory))\n}\n\n// @Summary 更新价格方案\n// @Description 更新价格方案\n// @Tags 价格方案\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateStatisticsCategoryReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.StatisticsCategoryVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/statistics-category/update [post]\nfunc (controller *StatisticsCategoryController) UpdateStatisticsCategory(ctx *gin.Context) {\n\treqDto := req.UpdateStatisticsCategoryReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\tstatisticsCategory, err := statisticsCategoryService.FindStatisticsCategoryById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tif reqDto.Name != nil {\n\t\tstatisticsCategory.Name = reqDto.Name\n\t}\n\tif reqDto.VenueId != nil {\n\t\tstatisticsCategory.VenueId = reqDto.VenueId\n\t}\n\tif reqDto.PricePlanIds != nil {\n\t\tstatisticsCategory.PricePlanIds = reqDto.PricePlanIds\n\t}\n\n\terr = statisticsCategoryService.UpdateStatisticsCategory(ctx, statisticsCategory)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, statisticsCategoryTransfer.PoToVo(*statisticsCategory))\n}\n\n// @Summary 删除价格方案\n// @Description 删除价格方案\n// @Tags 价格方案\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteStatisticsCategoryReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/statistics-category/delete [post]\nfunc (controller *StatisticsCategoryController) DeleteStatisticsCategory(ctx *gin.Context) {\n\treqDto := req.DeleteStatisticsCategoryReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = statisticsCategoryService.DeleteStatisticsCategory(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询价格方案\n// @Description 查询价格方案\n// @Tags 价格方案\n// @Accept json\n// @Produce json\n// @Param body body req.QueryStatisticsCategoryReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.StatisticsCategoryVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/statistics-category/query [post]\nfunc (controller *StatisticsCategoryController) QueryStatisticsCategories(ctx *gin.Context) {\n\treqDto := req.QueryStatisticsCategoryReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := statisticsCategoryService.FindAllStatisticsCategory(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.StatisticsCategoryVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, statisticsCategoryTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询价格方案列表\n// @Description 查询价格方案列表\n// @Tags 价格方案\n// @Accept json\n// @Produce json\n// @Param body body req.QueryStatisticsCategoryReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.StatisticsCategoryVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/statistics-category/list [post]\nfunc (a *StatisticsCategoryController) ListStatisticsCategories(ctx *gin.Context) {\n\treqDto := req.QueryStatisticsCategoryReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := statisticsCategoryService.FindAllStatisticsCategoryWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.StatisticsCategoryVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.StatisticsCategoryVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, statisticsCategoryTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype StatisticsCategoryRoute struct {\n}\n\nfunc (s *StatisticsCategoryRoute) InitStatisticsCategoryRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tstatisticsCategoryController := controller.StatisticsCategoryController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/statistics-category/add\", statisticsCategoryController.AddStatisticsCategory)       //add\n\t\troute.POST(\"/api/statistics-category/update\", statisticsCategoryController.UpdateStatisticsCategory)   //update\n\t\troute.POST(\"/api/statistics-category/delete\", statisticsCategoryController.DeleteStatisticsCategory)   //delete\n\t\troute.POST(\"/api/statistics-category/query\", statisticsCategoryController.QueryStatisticsCategories)   //query\n\t\troute.POST(\"/api/statistics-category/list\", statisticsCategoryController.ListStatisticsCategories)     //list\n\t}\n}\n"}]