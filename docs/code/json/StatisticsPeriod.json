[{"po": "package po\n\n// StatisticsPeriod 统计周期实体\ntype StatisticsPeriod struct {\n\tId          *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`           // ID\n\tName        *string `gorm:\"column:name;type:varchar(64);default:''\" json:\"name\"`               // 名称\n\tDescription *string `gorm:\"column:description;type:varchar(255);default:''\" json:\"description\"` // 描述\n\tCtime       *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                     // 创建时间戳\n\tUtime       *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                     // 更新时间戳\n\tState       *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                     // 状态值\n\tVersion     *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`                 // 版本号\n}\n\n// TableName 设置表名\nfunc (StatisticsPeriod) TableName() string {\n\treturn \"statistics_period\"\n}\n\nfunc (s StatisticsPeriod) GetId() string {\n\treturn *s.Id\n}", "vo": "package vo\n\n// StatisticsPeriodVO 统计周期值对象\ntype StatisticsPeriodVO struct {\n\tId          string `json:\"id\"`          // ID\n\tName        string `json:\"name\"`        // 名称\n\tDescription string `json:\"description\"` // 描述\n\tCtime       int64  `json:\"ctime\"`       // 创建时间戳\n\tUtime       int64  `json:\"utime\"`       // 更新时间戳\n\tState       int    `json:\"state\"`       // 状态值\n\tVersion     int    `json:\"version\"`     // 版本号\n}", "req_add": "package req\n\n// AddStatisticsPeriodReqDto 创建统计周期请求DTO\ntype AddStatisticsPeriodReqDto struct {\n\tName        *string `json:\"name\"`        // 名称\n\tDescription *string `json:\"description\"` // 描述\n}", "req_update": "package req\n\ntype UpdateStatisticsPeriodReqDto struct {\n\tId          *string `json:\"id\"`          // ID\n\tName        *string `json:\"name\"`        // 名称\n\tDescription *string `json:\"description\"` // 描述\n}", "req_delete": "package req\n\ntype DeleteStatisticsPeriodReqDto struct {\n\tId *string `json:\"id\"` // ID\n}", "req_query": "package req\n\ntype QueryStatisticsPeriodReqDto struct {\n\tId          *string `json:\"id\"`          // ID\n\tName        *string `json:\"name\"`        // 名称\n\tDescription *string `json:\"description\"` // 描述\n\tPageNum     *int    `json:\"pageNum\"`     // 页码\n\tPageSize    *int    `json:\"pageSize\"`    // 每页记录数\n}", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype StatisticsPeriodTransfer struct {\n}\n\nfunc (transfer *StatisticsPeriodTransfer) PoToVo(po po.StatisticsPeriod) vo.StatisticsPeriodVO {\n\tvo := vo.StatisticsPeriodVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *StatisticsPeriodTransfer) VoToPo(vo vo.StatisticsPeriodVO) po.StatisticsPeriod {\n\tpo := po.StatisticsPeriod{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype StatisticsPeriodService struct {\n}\n\nfunc (service *StatisticsPeriodService) CreateStatisticsPeriod(logCtx *gin.Context, statisticsPeriod *po.StatisticsPeriod) error {\n\treturn Save(statisticsPeriod)\n}\n\nfunc (service *StatisticsPeriodService) UpdateStatisticsPeriod(logCtx *gin.Context, statisticsPeriod *po.StatisticsPeriod) error {\n\treturn Update(statisticsPeriod)\n}\n\nfunc (service *StatisticsPeriodService) DeleteStatisticsPeriod(logCtx *gin.Context, id string) error {\n\treturn Delete(po.StatisticsPeriod{Id: &id})\n}\n\nfunc (service *StatisticsPeriodService) FindStatisticsPeriodById(logCtx *gin.Context, id string) (statisticsPeriod *po.StatisticsPeriod, err error) {\n\tstatisticsPeriod = &po.StatisticsPeriod{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(statisticsPeriod).Error\n\treturn\n}\n\nfunc (service *StatisticsPeriodService) FindAllStatisticsPeriod(logCtx *gin.Context, reqDto *req.QueryStatisticsPeriodReqDto) (list *[]po.StatisticsPeriod, err error) {\n\tdb := model.DBSlave.Self.Model(&po.StatisticsPeriod{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name LIKE ?\", \"%\"+*reqDto.Name+\"%\")\n\t}\n\tif reqDto.Description != nil && *reqDto.Description != \"\" {\n\t\tdb = db.Where(\"description LIKE ?\", \"%\"+*reqDto.Description+\"%\")\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.StatisticsPeriod{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *StatisticsPeriodService) FindAllStatisticsPeriodWithPagination(logCtx *gin.Context, reqDto *req.QueryStatisticsPeriodReqDto) (list *[]po.StatisticsPeriod, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.StatisticsPeriod{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name LIKE ?\", \"%\"+*reqDto.Name+\"%\")\n\t}\n\tif reqDto.Description != nil && *reqDto.Description != \"\" {\n\t\tdb = db.Where(\"description LIKE ?\", \"%\"+*reqDto.Description+\"%\")\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.StatisticsPeriod{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype StatisticsPeriodController struct{}\n\nvar (\n\tstatisticsPeriodService  = impl.StatisticsPeriodService{}\n\tstatisticsPeriodTransfer = transfer.StatisticsPeriodTransfer{}\n)\n\n// @Summary 添加统计周期\n// @Description 添加统计周期\n// @Tags 统计周期\n// @Accept json\n// @Produce json\n// @Param body body req.AddStatisticsPeriodReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.StatisticsPeriodVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/statistics-period/add [post]\nfunc (controller *StatisticsPeriodController) AddStatisticsPeriod(ctx *gin.Context) {\n\treqDto := req.AddStatisticsPeriodReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tstatisticsPeriod := po.StatisticsPeriod{}\n\tif reqDto.Name != nil {\n\t\tstatisticsPeriod.Name = reqDto.Name\n\t}\n\tif reqDto.Description != nil {\n\t\tstatisticsPeriod.Description = reqDto.Description\n\t}\n\terr = statisticsPeriodService.CreateStatisticsPeriod(ctx, &statisticsPeriod)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, statisticsPeriodTransfer.PoToVo(statisticsPeriod))\n}\n\n// @Summary 更新统计周期\n// @Description 更新统计周期\n// @Tags 统计周期\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateStatisticsPeriodReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.StatisticsPeriodVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/statistics-period/update [post]\nfunc (controller *StatisticsPeriodController) UpdateStatisticsPeriod(ctx *gin.Context) {\n\treqDto := req.UpdateStatisticsPeriodReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\tstatisticsPeriod, err := statisticsPeriodService.FindStatisticsPeriodById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Name != nil {\n\t\tstatisticsPeriod.Name = reqDto.Name\n\t}\n\tif reqDto.Description != nil {\n\t\tstatisticsPeriod.Description = reqDto.Description\n\t}\n\terr = statisticsPeriodService.UpdateStatisticsPeriod(ctx, statisticsPeriod)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, statisticsPeriodTransfer.PoToVo(*statisticsPeriod))\n}\n\n// @Summary 删除统计周期\n// @Description 删除统计周期\n// @Tags 统计周期\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteStatisticsPeriodReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/statistics-period/delete [post]\nfunc (controller *StatisticsPeriodController) DeleteStatisticsPeriod(ctx *gin.Context) {\n\treqDto := req.DeleteStatisticsPeriodReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = statisticsPeriodService.DeleteStatisticsPeriod(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询统计周期\n// @Description 查询统计周期\n// @Tags 统计周期\n// @Accept json\n// @Produce json\n// @Param body body req.QueryStatisticsPeriodReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.StatisticsPeriodVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/statistics-period/query [post]\nfunc (controller *StatisticsPeriodController) QueryStatisticsPeriods(ctx *gin.Context) {\n\treqDto := req.QueryStatisticsPeriodReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := statisticsPeriodService.FindAllStatisticsPeriod(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.StatisticsPeriodVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, statisticsPeriodTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询统计周期列表\n// @Description 查询统计周期列表\n// @Tags 统计周期\n// @Accept json\n// @Produce json\n// @Param body body req.QueryStatisticsPeriodReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.StatisticsPeriodVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/statistics-period/list [post]\nfunc (a *StatisticsPeriodController) ListStatisticsPeriods(ctx *gin.Context) {\n\treqDto := req.QueryStatisticsPeriodReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := statisticsPeriodService.FindAllStatisticsPeriodWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.StatisticsPeriodVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.StatisticsPeriodVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, statisticsPeriodTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype StatisticsPeriodRoute struct {\n}\n\nfunc (s *StatisticsPeriodRoute) InitStatisticsPeriodRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tstatisticsPeriodController := controller.StatisticsPeriodController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/statistics-period/add\", statisticsPeriodController.AddStatisticsPeriod)       //add\n\t\troute.POST(\"/api/statistics-period/update\", statisticsPeriodController.UpdateStatisticsPeriod)   //update\n\t\troute.POST(\"/api/statistics-period/delete\", statisticsPeriodController.DeleteStatisticsPeriod)   //delete\n\t\troute.POST(\"/api/statistics-period/query\", statisticsPeriodController.QueryStatisticsPeriods)    //query\n\t\troute.POST(\"/api/statistics-period/list\", statisticsPeriodController.ListStatisticsPeriods)     //list\n\t}\n}"}]