[{"po": "package po\n\n// Report 报表实体\ntype Report struct {\n\tId            *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`              // ID\n\tReportId      *string `gorm:\"column:report_id;type:varchar(64);default:''\" json:\"reportId\"`      // 报表ID\n\tGenerationDate *int64  `gorm:\"column:generation_date;type:int;default:0\" json:\"generationDate\"` // 生成日期\n\tType          *string `gorm:\"column:type;type:varchar(64);default:''\" json:\"type\"`            // 类型\n\tCtime         *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                    // 创建时间戳\n\tUtime         *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                    // 更新时间戳\n\tState         *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                    // 状态值\n\tVersion       *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`                // 版本号\n}\n\n// TableName 设置表名\nfunc (Report) TableName() string {\n\treturn \"report\"\n}\n\nfunc (r Report) GetId() string {\n\treturn *r.Id\n}\n", "vo": "package vo\n\n// ReportVO 报表信息值对象\ntype ReportVO struct {\n\tId            string `json:\"id\"`            // ID\n\tReportId      string `json:\"reportId\"`      // 报表ID\n\tGenerationDate int64  `json:\"generationDate\"` // 生成日期\n\tType          string `json:\"type\"`          // 类型\n\tCtime         int64  `json:\"ctime\"`         // 创建时间戳\n\tUtime         int64  `json:\"utime\"`         // 更新时间戳\n\tState         int    `json:\"state\"`         // 状态值\n\tVersion       int    `json:\"version\"`       // 版本号\n}\n", "req_add": "package req\n\n// AddReportReqDto 创建报表请求DTO\ntype AddReportReqDto struct {\n\tReportId      *string `json:\"reportId\"`      // 报表ID\n\tGenerationDate *int64  `json:\"generationDate\"` // 生成日期\n\tType          *string `json:\"type\"`          // 类型\n}\n", "req_update": "package req\n\ntype UpdateReportReqDto struct {\n\tId            *string `json:\"id\"`            // ID\n\tReportId      *string `json:\"reportId\"`      // 报表ID\n\tGenerationDate *int64  `json:\"generationDate\"` // 生成日期\n\tType          *string `json:\"type\"`          // 类型\n}\n", "req_delete": "package req\n\ntype DeleteReportReqDto struct {\n\tId *string `json:\"id\"` // ID\n}\n", "req_query": "package req\n\ntype QueryReportReqDto struct {\n\tId            *string `json:\"id\"`            // ID\n\tReportId      *string `json:\"reportId\"`      // 报表ID\n\tGenerationDate *int64  `json:\"generationDate\"` // 生成日期\n\tType          *string `json:\"type\"`          // 类型\n\tPageNum       *int    `json:\"pageNum\"`       // 页码\n\tPageSize      *int    `json:\"pageSize\"`      // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype ReportTransfer struct {\n}\n\nfunc (transfer *ReportTransfer) PoToVo(po po.Report) vo.ReportVO {\n\tvo := vo.ReportVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *ReportTransfer) VoToPo(vo vo.ReportVO) po.Report {\n\tpo := po.Report{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype ReportService struct {\n}\n\nfunc (service *ReportService) CreateReport(logCtx *gin.Context, report *po.Report) error {\n\treturn Save(report)\n}\n\nfunc (service *ReportService) UpdateReport(logCtx *gin.Context, report *po.Report) error {\n\treturn Update(report)\n}\n\nfunc (service *ReportService) DeleteReport(logCtx *gin.Context, id string) error {\n\treturn Delete(po.Report{Id: &id})\n}\n\nfunc (service *ReportService) FindReportById(logCtx *gin.Context, id string) (report *po.Report, err error) {\n\treport = &po.Report{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(report).Error\n\treturn\n}\n\nfunc (service *ReportService) FindAllReport(logCtx *gin.Context, reqDto *req.QueryReportReqDto) (list *[]po.Report, err error) {\n\tdb := model.DBSlave.Self.Model(&po.Report{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.Report{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *ReportService) FindAllReportWithPagination(logCtx *gin.Context, reqDto *req.QueryReportReqDto) (list *[]po.Report, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.Report{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.Report{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype ReportController struct{}\n\nvar (\n\treportService  = impl.ReportService{}\n\treportTransfer = transfer.ReportTransfer{}\n)\n\n// @Summary 添加报表\n// @Description 添加报表\n// @Tags 报表\n// @Accept json\n// @Produce json\n// @Param body body req.AddReportReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.ReportVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/report/add [post]\nfunc (controller *ReportController) AddReport(ctx *gin.Context) {\n\treqDto := req.AddReportReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\treport := po.Report{}\n\tif reqDto.ReportId != nil {\n\t\treport.ReportId = reqDto.ReportId\n\t}\n\tif reqDto.GenerationDate != nil {\n\t\treport.GenerationDate = reqDto.GenerationDate\n\t}\n\tif reqDto.Type != nil {\n\t\treport.Type = reqDto.Type\n\t}\n\terr = reportService.CreateReport(ctx, &report)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, reportTransfer.PoToVo(report))\n}\n\n// @Summary 更新报表\n// @Description 更新报表\n// @Tags 报表\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateReportReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.ReportVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/report/update [post]\nfunc (controller *ReportController) UpdateReport(ctx *gin.Context) {\n\treqDto := req.UpdateReportReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\treport, err := reportService.FindReportById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.ReportId != nil {\n\t\treport.ReportId = reqDto.ReportId\n\t}\n\tif reqDto.GenerationDate != nil {\n\t\treport.GenerationDate = reqDto.GenerationDate\n\t}\n\tif reqDto.Type != nil {\n\t\treport.Type = reqDto.Type\n\t}\n\terr = reportService.UpdateReport(ctx, report)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, reportTransfer.PoToVo(*report))\n}\n\n// @Summary 删除报表\n// @Description 删除报表\n// @Tags 报表\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteReportReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/report/delete [post]\nfunc (controller *ReportController) DeleteReport(ctx *gin.Context) {\n\treqDto := req.DeleteReportReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = reportService.DeleteReport(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询报表\n// @Description 查询报表\n// @Tags 报表\n// @Accept json\n// @Produce json\n// @Param body body req.QueryReportReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.ReportVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/report/query [post]\nfunc (controller *ReportController) QueryReports(ctx *gin.Context) {\n\treqDto := req.QueryReportReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := reportService.FindAllReport(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.ReportVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, reportTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询报表列表\n// @Description 查询报表列表\n// @Tags 报表\n// @Accept json\n// @Produce json\n// @Param body body req.QueryReportReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.ReportVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/report/list [post]\nfunc (a *ReportController) ListReports(ctx *gin.Context) {\n\treqDto := req.QueryReportReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := reportService.FindAllReportWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.ReportVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.ReportVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, reportTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype ReportRoute struct {\n}\n\nfunc (s *ReportRoute) InitReportRouter(g *gin.Engine) {\n\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\treportController := controller.ReportController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/report/add\", reportController.AddReport)    //add\n\t\troute.POST(\"/api/report/update\", reportController.UpdateReport) //update\n\t\troute.POST(\"/api/report/delete\", reportController.DeleteReport) //delete\n\t\troute.POST(\"/api/report/query\", reportController.QueryReports)     //query\n\t\troute.POST(\"/api/report/list\", reportController.ListReports)     //list\n\t}\n}\n"}]