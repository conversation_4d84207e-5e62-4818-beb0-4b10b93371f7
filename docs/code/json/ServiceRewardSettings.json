[{"po": "package po\n\n// ServiceRewardSettings 服务奖励设置实体\ntype ServiceRewardSettings struct {\n\tId         *string  `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`          // ID\n\tEnabled    *bool    `gorm:\"column:enabled;type:tinyint(1);default:0\" json:\"enabled\"`        // 是否启用\n\tSplitRatio *float32 `gorm:\"column:split_ratio;type:float;default:0\" json:\"splitRatio\"`      // 分成比例\n\tCtime      *int64   `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                   // 创建时间戳\n\tUtime      *int64   `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                   // 更新时间戳\n\tState      *int     `gorm:\"column:state;type:int;default:0\" json:\"state\"`                   // 状态值\n\tVersion    *int     `gorm:\"column:version;type:int;default:0\" json:\"version\"`               // 版本号\n}\n\n// TableName 设置表名\nfunc (ServiceRewardSettings) TableName() string {\n\treturn \"service_reward_settings\"\n}\n\nfunc (s ServiceRewardSettings) GetId() string {\n\treturn *s.Id\n}\n", "req_add": "package req\n\n// AddServiceRewardSettingsReqDto 创建服务奖励设置请求DTO\ntype AddServiceRewardSettingsReqDto struct {\n\tEnabled    *bool    `json:\"enabled\"`    // 是否启用\n\tSplitRatio *float32 `json:\"splitRatio\"` // 分成比例\n}\n", "req_update": "package req\n\ntype UpdateServiceRewardSettingsReqDto struct {\n\tId         *string  `json:\"id\"`         // ID\n\tEnabled    *bool    `json:\"enabled\"`    // 是否启用\n\tSplitRatio *float32 `json:\"splitRatio\"` // 分成比例\n}\n", "req_delete": "package req\n\ntype DeleteServiceRewardSettingsReqDto struct {\n\tId *string `json:\"id\"` // ID\n}\n", "req_query": "package req\n\ntype QueryServiceRewardSettingsReqDto struct {\n\tId         *string  `json:\"id\"`         // ID\n\tEnabled    *bool    `json:\"enabled\"`    // 是否启用\n\tSplitRatio *float32 `json:\"splitRatio\"` // 分成比例\n\tPageNum    *int     `json:\"pageNum\"`    // 页码\n\tPageSize   *int     `json:\"pageSize\"`   // 每页记录数\n}\n", "vo": "package vo\n\n// ServiceRewardSettingsVO 服务奖励设置值对象\ntype ServiceRewardSettingsVO struct {\n\tId         string  `json:\"id\"`         // ID\n\tEnabled    bool    `json:\"enabled\"`    // 是否启用\n\tSplitRatio float32 `json:\"splitRatio\"` // 分成比例\n\tCtime      int64   `json:\"ctime\"`      // 创建时间戳\n\tUtime      int64   `json:\"utime\"`      // 更新时间戳\n\tState      int     `json:\"state\"`      // 状态值\n\tVersion    int     `json:\"version\"`    // 版本号\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype ServiceRewardSettingsTransfer struct {\n}\n\nfunc (transfer *ServiceRewardSettingsTransfer) PoToVo(po po.ServiceRewardSettings) vo.ServiceRewardSettingsVO {\n\tvo := vo.ServiceRewardSettingsVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *ServiceRewardSettingsTransfer) VoToPo(vo vo.ServiceRewardSettingsVO) po.ServiceRewardSettings {\n\tpo := po.ServiceRewardSettings{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype ServiceRewardSettingsService struct {\n}\n\nfunc (service *ServiceRewardSettingsService) CreateServiceRewardSettings(logCtx *gin.Context, settings *po.ServiceRewardSettings) error {\n\treturn Save(settings)\n}\n\nfunc (service *ServiceRewardSettingsService) UpdateServiceRewardSettings(logCtx *gin.Context, settings *po.ServiceRewardSettings) error {\n\treturn Update(settings)\n}\n\nfunc (service *ServiceRewardSettingsService) DeleteServiceRewardSettings(logCtx *gin.Context, id string) error {\n\treturn Delete(po.ServiceRewardSettings{Id: &id})\n}\n\nfunc (service *ServiceRewardSettingsService) FindServiceRewardSettingsById(logCtx *gin.Context, id string) (settings *po.ServiceRewardSettings, err error) {\n\tsettings = &po.ServiceRewardSettings{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(settings).Error\n\treturn\n}\n\nfunc (service *ServiceRewardSettingsService) FindAllServiceRewardSettings(logCtx *gin.Context, reqDto *req.QueryServiceRewardSettingsReqDto) (list *[]po.ServiceRewardSettings, err error) {\n\tdb := model.DBSlave.Self.Model(&po.ServiceRewardSettings{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Enabled != nil {\n\t\tdb = db.Where(\"enabled=?\", *reqDto.Enabled)\n\t}\n\tif reqDto.SplitRatio != nil {\n\t\tdb = db.Where(\"split_ratio=?\", *reqDto.SplitRatio)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.ServiceRewardSettings{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *ServiceRewardSettingsService) FindAllServiceRewardSettingsWithPagination(logCtx *gin.Context, reqDto *req.QueryServiceRewardSettingsReqDto) (list *[]po.ServiceRewardSettings, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.ServiceRewardSettings{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Enabled != nil {\n\t\tdb = db.Where(\"enabled=?\", *reqDto.Enabled)\n\t}\n\tif reqDto.SplitRatio != nil {\n\t\tdb = db.Where(\"split_ratio=?\", *reqDto.SplitRatio)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.ServiceRewardSettings{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype ServiceRewardSettingsController struct{}\n\nvar (\n\tserviceRewardSettingsService  = impl.ServiceRewardSettingsService{}\n\tserviceRewardSettingsTransfer = transfer.ServiceRewardSettingsTransfer{}\n)\n\n// @Summary 添加服务奖励设置\n// @Description 添加服务奖励设置\n// @Tags 服务奖励设置\n// @Accept json\n// @Produce json\n// @Param body body req.AddServiceRewardSettingsReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.ServiceRewardSettingsVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/serviceRewardSettings/add [post]\nfunc (controller *ServiceRewardSettingsController) AddServiceRewardSettings(ctx *gin.Context) {\n\treqDto := req.AddServiceRewardSettingsReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tsettings := po.ServiceRewardSettings{}\n\tif reqDto.Enabled != nil {\n\t\tsettings.Enabled = reqDto.Enabled\n\t}\n\tif reqDto.SplitRatio != nil {\n\t\tsettings.SplitRatio = reqDto.SplitRatio\n\t}\n\terr = serviceRewardSettingsService.CreateServiceRewardSettings(ctx, &settings)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, serviceRewardSettingsTransfer.PoToVo(settings))\n}\n\n// @Summary 更新服务奖励设置\n// @Description 更新服务奖励设置\n// @Tags 服务奖励设置\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateServiceRewardSettingsReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.ServiceRewardSettingsVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/serviceRewardSettings/update [post]\nfunc (controller *ServiceRewardSettingsController) UpdateServiceRewardSettings(ctx *gin.Context) {\n\treqDto := req.UpdateServiceRewardSettingsReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\tsettings, err := serviceRewardSettingsService.FindServiceRewardSettingsById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Enabled != nil {\n\t\tsettings.Enabled = reqDto.Enabled\n\t}\n\tif reqDto.SplitRatio != nil {\n\t\tsettings.SplitRatio = reqDto.SplitRatio\n\t}\n\terr = serviceRewardSettingsService.UpdateServiceRewardSettings(ctx, settings)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, serviceRewardSettingsTransfer.PoToVo(*settings))\n}\n\n// @Summary 删除服务奖励设置\n// @Description 删除服务奖励设置\n// @Tags 服务奖励设置\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteServiceRewardSettingsReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/serviceRewardSettings/delete [post]\nfunc (controller *ServiceRewardSettingsController) DeleteServiceRewardSettings(ctx *gin.Context) {\n\treqDto := req.DeleteServiceRewardSettingsReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = serviceRewardSettingsService.DeleteServiceRewardSettings(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询服务奖励设置\n// @Description 查询服务奖励设置\n// @Tags 服务奖励设置\n// @Accept json\n// @Produce json\n// @Param body body req.QueryServiceRewardSettingsReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.ServiceRewardSettingsVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/serviceRewardSettings/query [post]\nfunc (controller *ServiceRewardSettingsController) QueryServiceRewardSettings(ctx *gin.Context) {\n\treqDto := req.QueryServiceRewardSettingsReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := serviceRewardSettingsService.FindAllServiceRewardSettings(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.ServiceRewardSettingsVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, serviceRewardSettingsTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询服务奖励设置列表\n// @Description 查询服务奖励设置列表\n// @Tags 服务奖励设置\n// @Accept json\n// @Produce json\n// @Param body body req.QueryServiceRewardSettingsReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.ServiceRewardSettingsVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/serviceRewardSettings/list [post]\nfunc (a *ServiceRewardSettingsController) ListServiceRewardSettings(ctx *gin.Context) {\n\treqDto := req.QueryServiceRewardSettingsReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := serviceRewardSettingsService.FindAllServiceRewardSettingsWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.ServiceRewardSettingsVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.ServiceRewardSettingsVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, serviceRewardSettingsTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype ServiceRewardSettingsRoute struct {\n}\n\nfunc (s *ServiceRewardSettingsRoute) InitServiceRewardSettingsRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tserviceRewardSettingsController := controller.ServiceRewardSettingsController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/serviceRewardSettings/add\", serviceRewardSettingsController.AddServiceRewardSettings)       //add\n\t\troute.POST(\"/api/serviceRewardSettings/update\", serviceRewardSettingsController.UpdateServiceRewardSettings)   //update\n\t\troute.POST(\"/api/serviceRewardSettings/delete\", serviceRewardSettingsController.DeleteServiceRewardSettings)   //delete\n\t\troute.POST(\"/api/serviceRewardSettings/query\", serviceRewardSettingsController.QueryServiceRewardSettings)     //query\n\t\troute.POST(\"/api/serviceRewardSettings/list\", serviceRewardSettingsController.ListServiceRewardSettings)     //list\n\t}\n}\n"}]