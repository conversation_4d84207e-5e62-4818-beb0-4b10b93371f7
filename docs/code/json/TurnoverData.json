[{"po": "package po\n\n// TurnoverData 营业额数据实体\ntype TurnoverData struct {\n\tId                   *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`                    // ID\n\tDate                 *int64  `gorm:\"column:date;type:int;default:0\" json:\"date\"`                              // 日期\n\tPeriod               *string `gorm:\"column:period;type:varchar(64);default:''\" json:\"period\"`                   // 统计周期\n\tRoomReceivable       *int64  `gorm:\"column:room_receivable;type:int;default:0\" json:\"roomReceivable\"`           // 包厢应收\n\tRoomActualReceived   *int64  `gorm:\"column:room_actual_received;type:int;default:0\" json:\"roomActualReceived\"`   // 包厢实收\n\tProductReceivable    *int64  `gorm:\"column:product_receivable;type:int;default:0\" json:\"productReceivable\"`     // 产品应收\n\tProductActualReceived *int64  `gorm:\"column:product_actual_received;type:int;default:0\" json:\"productActualReceived\"` // 产品实收\n\tGiftAmount           *int64  `gorm:\"column:gift_amount;type:int;default:0\" json:\"giftAmount\"`                   // 赠送金额\n\tMemberRecharge       *int64  `gorm:\"column:member_recharge;type:int;default:0\" json:\"memberRecharge\"`           // 会员充值\n\tDiscountAmount       *int64  `gorm:\"column:discount_amount;type:int;default:0\" json:\"discountAmount\"`           // 折扣金额\n\tPointsExchangeAmount *int64  `gorm:\"column:points_exchange_amount;type:int;default:0\" json:\"pointsExchangeAmount\"` // 积分兑换金额\n\tTotalTurnover        *int64  `gorm:\"column:total_turnover;type:int;default:0\" json:\"totalTurnover\"`             // 总营业额\n\tCtime                *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                             // 创建时间戳\n\tUtime                *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                             // 更新时间戳\n\tState                *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                             // 状态值\n\tVersion              *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`                         // 版本号\n}\n\n// TableName 设置表名\nfunc (TurnoverData) TableName() string {\n\treturn \"turnover_data\"\n}\n\nfunc (t TurnoverData) GetId() string {\n\treturn *t.Id\n}\n", "vo": "package vo\n\n// TurnoverDataVO 营业额数据值对象\ntype TurnoverDataVO struct {\n\tId                   string `json:\"id\"`                    // ID\n\tDate                 int64  `json:\"date\"`                  // 日期\n\tPeriod               string `json:\"period\"`                // 统计周期\n\tRoomReceivable       int64  `json:\"roomReceivable\"`        // 包厢应收\n\tRoomActualReceived   int64  `json:\"roomActualReceived\"`    // 包厢实收\n\tProductReceivable    int64  `json:\"productReceivable\"`     // 产品应收\n\tProductActualReceived int64  `json:\"productActualReceived\"` // 产品实收\n\tGiftAmount           int64  `json:\"giftAmount\"`            // 赠送金额\n\tMemberRecharge       int64  `json:\"memberRecharge\"`        // 会员充值\n\tDiscountAmount       int64  `json:\"discountAmount\"`        // 折扣金额\n\tPointsExchangeAmount int64  `json:\"pointsExchangeAmount\"` // 积分兑换金额\n\tTotalTurnover        int64  `json:\"totalTurnover\"`         // 总营业额\n\tCtime                int64  `json:\"ctime\"`                 // 创建时间戳\n\tUtime                int64  `json:\"utime\"`                 // 更新时间戳\n\tState                int    `json:\"state\"`                 // 状态值\n\tVersion              int    `json:\"version\"`               // 版本号\n}\n", "req_add": "package req\n\n// AddTurnoverDataReqDto 创建营业额数据请求DTO\ntype AddTurnoverDataReqDto struct {\n\tDate                 *int64  `json:\"date\"`                  // 日期\n\tPeriod               *string `json:\"period\"`                // 统计周期\n\tRoomReceivable       *int64  `json:\"roomReceivable\"`        // 包厢应收\n\tRoomActualReceived   *int64  `json:\"roomActualReceived\"`    // 包厢实收\n\tProductReceivable    *int64  `json:\"productReceivable\"`     // 产品应收\n\tProductActualReceived *int64  `json:\"productActualReceived\"` // 产品实收\n\tGiftAmount           *int64  `json:\"giftAmount\"`            // 赠送金额\n\tMemberRecharge       *int64  `json:\"memberRecharge\"`        // 会员充值\n\tDiscountAmount       *int64  `json:\"discountAmount\"`        // 折扣金额\n\tPointsExchangeAmount *int64  `json:\"pointsExchangeAmount\"` // 积分兑换金额\n\tTotalTurnover        *int64  `json:\"totalTurnover\"`         // 总营业额\n}\n", "req_update": "package req\n\n// UpdateTurnoverDataReqDto 更新营业额数据请求DTO\ntype UpdateTurnoverDataReqDto struct {\n\tId                   *string `json:\"id\"`                    // ID\n\tDate                 *int64  `json:\"date\"`                  // 日期\n\tPeriod               *string `json:\"period\"`                // 统计周期\n\tRoomReceivable       *int64  `json:\"roomReceivable\"`        // 包厢应收\n\tRoomActualReceived   *int64  `json:\"roomActualReceived\"`    // 包厢实收\n\tProductReceivable    *int64  `json:\"productReceivable\"`     // 产品应收\n\tProductActualReceived *int64  `json:\"productActualReceived\"` // 产品实收\n\tGiftAmount           *int64  `json:\"giftAmount\"`            // 赠送金额\n\tMemberRecharge       *int64  `json:\"memberRecharge\"`        // 会员充值\n\tDiscountAmount       *int64  `json:\"discountAmount\"`        // 折扣金额\n\tPointsExchangeAmount *int64  `json:\"pointsExchangeAmount\"` // 积分兑换金额\n\tTotalTurnover        *int64  `json:\"totalTurnover\"`         // 总营业额\n}\n", "req_delete": "package req\n\ntype DeleteTurnoverDataReqDto struct {\n\tId *string `json:\"id\"` // ID\n}\n", "req_query": "package req\n\ntype QueryTurnoverDataReqDto struct {\n\tId                   *string `json:\"id\"`                    // ID\n\tDate                 *int64  `json:\"date\"`                  // 日期\n\tPeriod               *string `json:\"period\"`                // 统计周期\n\tRoomReceivable       *int64  `json:\"roomReceivable\"`        // 包厢应收\n\tRoomActualReceived   *int64  `json:\"roomActualReceived\"`    // 包厢实收\n\tProductReceivable    *int64  `json:\"productReceivable\"`     // 产品应收\n\tProductActualReceived *int64  `json:\"productActualReceived\"` // 产品实收\n\tGiftAmount           *int64  `json:\"giftAmount\"`            // 赠送金额\n\tMemberRecharge       *int64  `json:\"memberRecharge\"`        // 会员充值\n\tDiscountAmount       *int64  `json:\"discountAmount\"`        // 折扣金额\n\tPointsExchangeAmount *int64  `json:\"pointsExchangeAmount\"` // 积分兑换金额\n\tTotalTurnover        *int64  `json:\"totalTurnover\"`         // 总营业额\n\tPageNum              *int    `json:\"pageNum\"`               // 页码\n\tPageSize             *int    `json:\"pageSize\"`              // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype TurnoverDataTransfer struct {\n}\n\nfunc (transfer *TurnoverDataTransfer) PoToVo(po po.TurnoverData) vo.TurnoverDataVO {\n\tvo := vo.TurnoverDataVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *TurnoverDataTransfer) VoToPo(vo vo.TurnoverDataVO) po.TurnoverData {\n\tpo := po.TurnoverData{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype TurnoverDataService struct {\n}\n\nfunc (service *TurnoverDataService) CreateTurnoverData(logCtx *gin.Context, turnoverData *po.TurnoverData) error {\n\treturn Save(turnoverData)\n}\n\nfunc (service *TurnoverDataService) UpdateTurnoverData(logCtx *gin.Context, turnoverData *po.TurnoverData) error {\n\treturn Update(turnoverData)\n}\n\nfunc (service *TurnoverDataService) DeleteTurnoverData(logCtx *gin.Context, id string) error {\n\treturn Delete(po.TurnoverData{Id: &id})\n}\n\nfunc (service *TurnoverDataService) FindTurnoverDataById(logCtx *gin.Context, id string) (turnoverData *po.TurnoverData, err error) {\n\tturnoverData = &po.TurnoverData{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(turnoverData).Error\n\treturn\n}\n\nfunc (service *TurnoverDataService) FindAllTurnoverData(logCtx *gin.Context, reqDto *req.QueryTurnoverDataReqDto) (list *[]po.TurnoverData, err error) {\n\tdb := model.DBSlave.Self.Model(&po.TurnoverData{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Date != nil {\n\t\tdb = db.Where(\"date=?\", *reqDto.Date)\n\t}\n\tif reqDto.Period != nil && *reqDto.Period != \"\" {\n\t\tdb = db.Where(\"period=?\", *reqDto.Period)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.TurnoverData{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *TurnoverDataService) FindAllTurnoverDataWithPagination(logCtx *gin.Context, reqDto *req.QueryTurnoverDataReqDto) (list *[]po.TurnoverData, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.TurnoverData{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Date != nil {\n\t\tdb = db.Where(\"date=?\", *reqDto.Date)\n\t}\n\tif reqDto.Period != nil && *reqDto.Period != \"\" {\n\t\tdb = db.Where(\"period=?\", *reqDto.Period)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.TurnoverData{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype TurnoverDataController struct{}\n\nvar (\n\tturnoverDataService  = impl.TurnoverDataService{}\n\tturnoverDataTransfer = transfer.TurnoverDataTransfer{}\n)\n\n// @Summary 添加营业额数据\n// @Description 添加营业额数据\n// @Tags 营业额数据\n// @Accept json\n// @Produce json\n// @Param body body req.AddTurnoverDataReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.TurnoverDataVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/turnover-data/add [post]\nfunc (controller *TurnoverDataController) AddTurnoverData(ctx *gin.Context) {\n\treqDto := req.AddTurnoverDataReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tturnoverData := po.TurnoverData{}\n\tif reqDto.Date != nil {\n\t\tturnoverData.Date = reqDto.Date\n\t}\n\tif reqDto.Period != nil {\n\t\tturnoverData.Period = reqDto.Period\n\t}\n\tif reqDto.RoomReceivable != nil {\n\t\tturnoverData.RoomReceivable = reqDto.RoomReceivable\n\t}\n\tif reqDto.RoomActualReceived != nil {\n\t\tturnoverData.RoomActualReceived = reqDto.RoomActualReceived\n\t}\n\tif reqDto.ProductReceivable != nil {\n\t\tturnoverData.ProductReceivable = reqDto.ProductReceivable\n\t}\n\tif reqDto.ProductActualReceived != nil {\n\t\tturnoverData.ProductActualReceived = reqDto.ProductActualReceived\n\t}\n\tif reqDto.GiftAmount != nil {\n\t\tturnoverData.GiftAmount = reqDto.GiftAmount\n\t}\n\tif reqDto.MemberRecharge != nil {\n\t\tturnoverData.MemberRecharge = reqDto.MemberRecharge\n\t}\n\tif reqDto.DiscountAmount != nil {\n\t\tturnoverData.DiscountAmount = reqDto.DiscountAmount\n\t}\n\tif reqDto.PointsExchangeAmount != nil {\n\t\tturnoverData.PointsExchangeAmount = reqDto.PointsExchangeAmount\n\t}\n\tif reqDto.TotalTurnover != nil {\n\t\tturnoverData.TotalTurnover = reqDto.TotalTurnover\n\t}\n\n\terr = turnoverDataService.CreateTurnoverData(ctx, &turnoverData)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, turnoverDataTransfer.PoToVo(turnoverData))\n}\n\n// @Summary 更新营业额数据\n// @Description 更新营业额数据\n// @Tags 营业额数据\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateTurnoverDataReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.TurnoverDataVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/turnover-data/update [post]\nfunc (controller *TurnoverDataController) UpdateTurnoverData(ctx *gin.Context) {\n\treqDto := req.UpdateTurnoverDataReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\tturnoverData, err := turnoverDataService.FindTurnoverDataById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tif reqDto.Date != nil {\n\t\tturnoverData.Date = reqDto.Date\n\t}\n\tif reqDto.Period != nil {\n\t\tturnoverData.Period = reqDto.Period\n\t}\n\tif reqDto.RoomReceivable != nil {\n\t\tturnoverData.RoomReceivable = reqDto.RoomReceivable\n\t}\n\tif reqDto.RoomActualReceived != nil {\n\t\tturnoverData.RoomActualReceived = reqDto.RoomActualReceived\n\t}\n\tif reqDto.ProductReceivable != nil {\n\t\tturnoverData.ProductReceivable = reqDto.ProductReceivable\n\t}\n\tif reqDto.ProductActualReceived != nil {\n\t\tturnoverData.ProductActualReceived = reqDto.ProductActualReceived\n\t}\n\tif reqDto.GiftAmount != nil {\n\t\tturnoverData.GiftAmount = reqDto.GiftAmount\n\t}\n\tif reqDto.MemberRecharge != nil {\n\t\tturnoverData.MemberRecharge = reqDto.MemberRecharge\n\t}\n\tif reqDto.DiscountAmount != nil {\n\t\tturnoverData.DiscountAmount = reqDto.DiscountAmount\n\t}\n\tif reqDto.PointsExchangeAmount != nil {\n\t\tturnoverData.PointsExchangeAmount = reqDto.PointsExchangeAmount\n\t}\n\tif reqDto.TotalTurnover != nil {\n\t\tturnoverData.TotalTurnover = reqDto.TotalTurnover\n\t}\n\n\terr = turnoverDataService.UpdateTurnoverData(ctx, turnoverData)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, turnoverDataTransfer.PoToVo(*turnoverData))\n}\n\n// @Summary 删除营业额数据\n// @Description 删除营业额数据\n// @Tags 营业额数据\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteTurnoverDataReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/turnover-data/delete [post]\nfunc (controller *TurnoverDataController) DeleteTurnoverData(ctx *gin.Context) {\n\treqDto := req.DeleteTurnoverDataReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = turnoverDataService.DeleteTurnoverData(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询营业额数据\n// @Description 查询营业额数据\n// @Tags 营业额数据\n// @Accept json\n// @Produce json\n// @Param body body req.QueryTurnoverDataReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.TurnoverDataVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/turnover-data/query [post]\nfunc (controller *TurnoverDataController) QueryTurnoverData(ctx *gin.Context) {\n\treqDto := req.QueryTurnoverDataReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := turnoverDataService.FindAllTurnoverData(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.TurnoverDataVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, turnoverDataTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询营业额数据列表\n// @Description 查询营业额数据列表\n// @Tags 营业额数据\n// @Accept json\n// @Produce json\n// @Param body body req.QueryTurnoverDataReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.TurnoverDataVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/turnover-data/list [post]\nfunc (controller *TurnoverDataController) ListTurnoverData(ctx *gin.Context) {\n\treqDto := req.QueryTurnoverDataReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := turnoverDataService.FindAllTurnoverDataWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.TurnoverDataVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.TurnoverDataVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, turnoverDataTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype TurnoverDataRoute struct {\n}\n\nfunc (s *TurnoverDataRoute) InitTurnoverDataRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tturnoverDataController := controller.TurnoverDataController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/turnover-data/add\", turnoverDataController.AddTurnoverData)       // add\n\t\troute.POST(\"/api/turnover-data/update\", turnoverDataController.UpdateTurnoverData)   // update\n\t\troute.POST(\"/api/turnover-data/delete\", turnoverDataController.DeleteTurnoverData)   // delete\n\t\troute.POST(\"/api/turnover-data/query\", turnoverDataController.QueryTurnoverData)     // query\n\t\troute.POST(\"/api/turnover-data/list\", turnoverDataController.ListTurnoverData)       // list\n\t}\n}\n"}]