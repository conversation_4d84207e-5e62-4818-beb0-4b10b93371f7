[{"po": "package po\n\n// Sequencer 点歌排序器实体\ntype Sequencer struct {\n\tId          *string  `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`          // ID\n\tModel       *string  `gorm:\"column:model;type:varchar(64);default:''\" json:\"model\"`           // 型号\n\tMacAddress  *string  `gorm:\"column:mac_address;type:varchar(64);default:''\" json:\"macAddress\"` // MAC地址\n\tIpAddress   *string  `gorm:\"column:ip_address;type:varchar(64);default:''\" json:\"ipAddress\"`   // IP地址\n\tStatus      *string  `gorm:\"column:status;type:varchar(64);default:''\" json:\"status\"`         // 状态\n\tTemperature *float32 `gorm:\"column:temperature;type:float;default:0\" json:\"temperature\"`     // 温度\n\tServerIP    *string  `gorm:\"column:server_ip;type:varchar(64);default:''\" json:\"serverIP\"`     // 服务器IP\n\tGateway     *string  `gorm:\"column:gateway;type:varchar(64);default:''\" json:\"gateway\"`       // 网关\n\tDelayTime   *int     `gorm:\"column:delay_time;type:int;default:0\" json:\"delayTime\"`         // 延迟时间\n\tCtime       *int64   `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                   // 创建时间戳\n\tUtime       *int64   `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                   // 更新时间戳\n\tState       *int     `gorm:\"column:state;type:int;default:0\" json:\"state\"`                   // 状态值\n\tVersion     *int     `gorm:\"column:version;type:int;default:0\" json:\"version\"`               // 版本号\n}\n\n// TableName 设置表名\nfunc (Sequencer) TableName() string {\n\treturn \"sequencer\"\n}\n\nfunc (s Sequencer) GetId() string {\n\treturn *s.Id\n}\n", "vo": "package vo\n\n// SequencerVO 点歌排序器值对象\ntype SequencerVO struct {\n\tId          string  `json:\"id\"`          // ID\n\tModel       string  `json:\"model\"`       // 型号\n\tMacAddress  string  `json:\"macAddress\"`  // MAC地址\n\tIpAddress   string  `json:\"ipAddress\"`   // IP地址\n\tStatus      string  `json:\"status\"`      // 状态\n\tTemperature float32 `json:\"temperature\"` // 温度\n\tServerIP    string  `json:\"serverIP\"`    // 服务器IP\n\tGateway     string  `json:\"gateway\"`     // 网关\n\tDelayTime   int     `json:\"delayTime\"`   // 延迟时间\n\tCtime       int64   `json:\"ctime\"`       // 创建时间戳\n\tUtime       int64   `json:\"utime\"`       // 更新时间戳\n\tState       int     `json:\"state\"`       // 状态值\n\tVersion     int     `json:\"version\"`     // 版本号\n}\n", "req_add": "package req\n\n// AddSequencerReqDto 创建点歌排序器请求DTO\ntype AddSequencerReqDto struct {\n\tModel       *string  `json:\"model\"`       // 型号\n\tMacAddress  *string  `json:\"macAddress\"`  // MAC地址\n\tIpAddress   *string  `json:\"ipAddress\"`   // IP地址\n\tStatus      *string  `json:\"status\"`      // 状态\n\tTemperature *float32 `json:\"temperature\"` // 温度\n\tServerIP    *string  `json:\"serverIP\"`    // 服务器IP\n\tGateway     *string  `json:\"gateway\"`     // 网关\n\tDelayTime   *int     `json:\"delayTime\"`   // 延迟时间\n}\n", "req_update": "package req\n\ntype UpdateSequencerReqDto struct {\n\tId          *string  `json:\"id\"`          // ID\n\tModel       *string  `json:\"model\"`       // 型号\n\tMacAddress  *string  `json:\"macAddress\"`  // MAC地址\n\tIpAddress   *string  `json:\"ipAddress\"`   // IP地址\n\tStatus      *string  `json:\"status\"`      // 状态\n\tTemperature *float32 `json:\"temperature\"` // 温度\n\tServerIP    *string  `json:\"serverIP\"`    // 服务器IP\n\tGateway     *string  `json:\"gateway\"`     // 网关\n\tDelayTime   *int     `json:\"delayTime\"`   // 延迟时间\n}\n", "req_delete": "package req\n\ntype DeleteSequencerReqDto struct {\n\tId *string `json:\"id\"` // ID\n}\n", "req_query": "package req\n\ntype QuerySequencerReqDto struct {\n\tId          *string  `json:\"id\"`          // ID\n\tModel       *string  `json:\"model\"`       // 型号\n\tMacAddress  *string  `json:\"macAddress\"`  // MAC地址\n\tIpAddress   *string  `json:\"ipAddress\"`   // IP地址\n\tStatus      *string  `json:\"status\"`      // 状态\n\tTemperature *float32 `json:\"temperature\"` // 温度\n\tServerIP    *string  `json:\"serverIP\"`    // 服务器IP\n\tGateway     *string  `json:\"gateway\"`     // 网关\n\tDelayTime   *int     `json:\"delayTime\"`   // 延迟时间\n\tPageNum     *int     `json:\"pageNum\"`     // 页码\n\tPageSize    *int     `json:\"pageSize\"`    // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype SequencerTransfer struct {\n}\n\nfunc (transfer *SequencerTransfer) PoToVo(po po.Sequencer) vo.SequencerVO {\n\tvo := vo.SequencerVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *SequencerTransfer) VoToPo(vo vo.SequencerVO) po.Sequencer {\n\tpo := po.Sequencer{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype SequencerService struct {\n}\n\nfunc (service *SequencerService) CreateSequencer(logCtx *gin.Context, sequencer *po.Sequencer) error {\n\treturn Save(sequencer)\n}\n\nfunc (service *SequencerService) UpdateSequencer(logCtx *gin.Context, sequencer *po.Sequencer) error {\n\treturn Update(sequencer)\n}\n\nfunc (service *SequencerService) DeleteSequencer(logCtx *gin.Context, id string) error {\n\treturn Delete(po.Sequencer{Id: &id})\n}\n\nfunc (service *SequencerService) FindSequencerById(logCtx *gin.Context, id string) (sequencer *po.Sequencer, err error) {\n\tsequencer = &po.Sequencer{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(sequencer).Error\n\treturn\n}\n\nfunc (service *SequencerService) FindAllSequencer(logCtx *gin.Context, reqDto *req.QuerySequencerReqDto) (list *[]po.Sequencer, err error) {\n\tdb := model.DBSlave.Self.Model(&po.Sequencer{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Model != nil && *reqDto.Model != \"\" {\n\t\tdb = db.Where(\"model=?\", *reqDto.Model)\n\t}\n\tif reqDto.MacAddress != nil && *reqDto.MacAddress != \"\" {\n\t\tdb = db.Where(\"mac_address=?\", *reqDto.MacAddress)\n\t}\n\tif reqDto.IpAddress != nil && *reqDto.IpAddress != \"\" {\n\t\tdb = db.Where(\"ip_address=?\", *reqDto.IpAddress)\n\t}\n\tif reqDto.Status != nil && *reqDto.Status != \"\" {\n\t\tdb = db.Where(\"status=?\", *reqDto.Status)\n\t}\n\tif reqDto.ServerIP != nil && *reqDto.ServerIP != \"\" {\n\t\tdb = db.Where(\"server_ip=?\", *reqDto.ServerIP)\n\t}\n\tif reqDto.Gateway != nil && *reqDto.Gateway != \"\" {\n\t\tdb = db.Where(\"gateway=?\", *reqDto.Gateway)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.Sequencer{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *SequencerService) FindAllSequencerWithPagination(logCtx *gin.Context, reqDto *req.QuerySequencerReqDto) (list *[]po.Sequencer, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.Sequencer{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Model != nil && *reqDto.Model != \"\" {\n\t\tdb = db.Where(\"model=?\", *reqDto.Model)\n\t}\n\tif reqDto.MacAddress != nil && *reqDto.MacAddress != \"\" {\n\t\tdb = db.Where(\"mac_address=?\", *reqDto.MacAddress)\n\t}\n\tif reqDto.IpAddress != nil && *reqDto.IpAddress != \"\" {\n\t\tdb = db.Where(\"ip_address=?\", *reqDto.IpAddress)\n\t}\n\tif reqDto.Status != nil && *reqDto.Status != \"\" {\n\t\tdb = db.Where(\"status=?\", *reqDto.Status)\n\t}\n\tif reqDto.ServerIP != nil && *reqDto.ServerIP != \"\" {\n\t\tdb = db.Where(\"server_ip=?\", *reqDto.ServerIP)\n\t}\n\tif reqDto.Gateway != nil && *reqDto.Gateway != \"\" {\n\t\tdb = db.Where(\"gateway=?\", *reqDto.Gateway)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.Sequencer{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype SequencerController struct{}\n\nvar (\n\tsequencerService  = impl.SequencerService{}\n\tsequencerTransfer = transfer.SequencerTransfer{}\n)\n\n// @Summary 添加点歌排序器\n// @Description 添加点歌排序器\n// @Tags 点歌排序器\n// @Accept json\n// @Produce json\n// @Param body body req.AddSequencerReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.SequencerVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/sequencer/add [post]\nfunc (controller *SequencerController) AddSequencer(ctx *gin.Context) {\n\treqDto := req.AddSequencerReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tsequencer := po.Sequencer{}\n\tif reqDto.Model != nil {\n\t\tsequencer.Model = reqDto.Model\n\t}\n\tif reqDto.MacAddress != nil {\n\t\tsequencer.MacAddress = reqDto.MacAddress\n\t}\n\tif reqDto.IpAddress != nil {\n\t\tsequencer.IpAddress = reqDto.IpAddress\n\t}\n\tif reqDto.Status != nil {\n\t\tsequencer.Status = reqDto.Status\n\t}\n\tif reqDto.Temperature != nil {\n\t\tsequencer.Temperature = reqDto.Temperature\n\t}\n\tif reqDto.ServerIP != nil {\n\t\tsequencer.ServerIP = reqDto.ServerIP\n\t}\n\tif reqDto.Gateway != nil {\n\t\tsequencer.Gateway = reqDto.Gateway\n\t}\n\tif reqDto.DelayTime != nil {\n\t\tsequencer.DelayTime = reqDto.DelayTime\n\t}\n\n\terr = sequencerService.CreateSequencer(ctx, &sequencer)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, sequencerTransfer.PoToVo(sequencer))\n}\n\n// @Summary 更新点歌排序器\n// @Description 更新点歌排序器\n// @Tags 点歌排序器\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateSequencerReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.SequencerVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/sequencer/update [post]\nfunc (controller *SequencerController) UpdateSequencer(ctx *gin.Context) {\n\treqDto := req.UpdateSequencerReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\n\tsequencer, err := sequencerService.FindSequencerById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tif reqDto.Model != nil {\n\t\tsequencer.Model = reqDto.Model\n\t}\n\tif reqDto.MacAddress != nil {\n\t\tsequencer.MacAddress = reqDto.MacAddress\n\t}\n\tif reqDto.IpAddress != nil {\n\t\tsequencer.IpAddress = reqDto.IpAddress\n\t}\n\tif reqDto.Status != nil {\n\t\tsequencer.Status = reqDto.Status\n\t}\n\tif reqDto.Temperature != nil {\n\t\tsequencer.Temperature = reqDto.Temperature\n\t}\n\tif reqDto.ServerIP != nil {\n\t\tsequencer.ServerIP = reqDto.ServerIP\n\t}\n\tif reqDto.Gateway != nil {\n\t\tsequencer.Gateway = reqDto.Gateway\n\t}\n\tif reqDto.DelayTime != nil {\n\t\tsequencer.DelayTime = reqDto.DelayTime\n\t}\n\n\terr = sequencerService.UpdateSequencer(ctx, sequencer)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, sequencerTransfer.PoToVo(*sequencer))\n}\n\n// @Summary 删除点歌排序器\n// @Description 删除点歌排序器\n// @Tags 点歌排序器\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteSequencerReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/sequencer/delete [post]\nfunc (controller *SequencerController) DeleteSequencer(ctx *gin.Context) {\n\treqDto := req.DeleteSequencerReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\terr = sequencerService.DeleteSequencer(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询点歌排序器\n// @Description 查询点歌排序器\n// @Tags 点歌排序器\n// @Accept json\n// @Produce json\n// @Param body body req.QuerySequencerReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.SequencerVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/sequencer/query [post]\nfunc (controller *SequencerController) QuerySequencers(ctx *gin.Context) {\n\treqDto := req.QuerySequencerReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tlist, err := sequencerService.FindAllSequencer(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tnewList := []vo.SequencerVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, sequencerTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询点歌排序器列表\n// @Description 查询点歌排序器列表\n// @Tags 点歌排序器\n// @Accept json\n// @Produce json\n// @Param body body req.QuerySequencerReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.SequencerVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/sequencer/list [post]\nfunc (controller *SequencerController) ListSequencers(ctx *gin.Context) {\n\treqDto := req.QuerySequencerReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\n\tlist, totalCount, err := sequencerService.FindAllSequencerWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tpage := vo.PageVO[[]vo.SequencerVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.SequencerVO{}\n\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, sequencerTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype SequencerRoute struct {\n}\n\nfunc (s *SequencerRoute) InitSequencerRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tsequencerController := controller.SequencerController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/sequencer/add\", sequencerController.AddSequencer)       // add\n\t\troute.POST(\"/api/sequencer/update\", sequencerController.UpdateSequencer) // update\n\t\troute.POST(\"/api/sequencer/delete\", sequencerController.DeleteSequencer) // delete\n\t\troute.POST(\"/api/sequencer/query\", sequencerController.QuerySequencers)  // query\n\t\troute.POST(\"/api/sequencer/list\", sequencerController.ListSequencers)   // list\n\t}\n}\n"}]