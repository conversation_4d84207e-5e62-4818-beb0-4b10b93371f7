[{"po": "package po\n\n// Warehouse 仓库实体\ntype Warehouse struct {\n\tId                    *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`                    // ID\n\tName                  *string `gorm:\"column:name;type:varchar(255);default:''\" json:\"name\"`                  // 仓库名称\n\tRemark               *string `gorm:\"column:remark;type:varchar(255);default:''\" json:\"remark\"`               // 备注\n\tIsDefaultLiquorStorage *bool   `gorm:\"column:is_default_liquor_storage;type:tinyint;default:0\" json:\"isDefaultLiquorStorage\"` // 是否为默认酒水仓库\n\tCtime                *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                  // 创建时间戳\n\tUtime                *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                  // 更新时间戳\n\tState                *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                  // 状态值\n\tVersion              *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`              // 版本号\n}\n\n// TableName 设置表名\nfunc (Warehouse) TableName() string {\n\treturn \"warehouse\"\n}\n\nfunc (w Warehouse) GetId() string {\n\treturn *w.Id\n}\n", "vo": "package vo\n\n// WarehouseVO 仓库信息值对象\ntype WarehouseVO struct {\n\tId                    string `json:\"id\"`                    // ID\n\tName                  string `json:\"name\"`                  // 仓库名称\n\tRemark               string `json:\"remark\"`               // 备注\n\tIsDefaultLiquorStorage bool   `json:\"isDefaultLiquorStorage\"` // 是否为默认酒水仓库\n\tCtime                int64  `json:\"ctime\"`                // 创建时间戳\n\tUtime                int64  `json:\"utime\"`                // 更新时间戳\n\tState                int    `json:\"state\"`                // 状态值\n\tVersion              int    `json:\"version\"`              // 版本号\n}\n", "req_add": "package req\n\n// AddWarehouseReqDto 创建仓库请求DTO\ntype AddWarehouseReqDto struct {\n\tName                  *string `json:\"name\"`                  // 仓库名称\n\tRemark               *string `json:\"remark\"`               // 备注\n\tIsDefaultLiquorStorage *bool   `json:\"isDefaultLiquorStorage\"` // 是否为默认酒水仓库\n}\n", "req_update": "package req\n\ntype UpdateWarehouseReqDto struct {\n\tId                    *string `json:\"id\"`                    // 仓库ID\n\tName                  *string `json:\"name\"`                  // 仓库名称\n\tRemark               *string `json:\"remark\"`               // 备注\n\tIsDefaultLiquorStorage *bool   `json:\"isDefaultLiquorStorage\"` // 是否为默认酒水仓库\n}\n", "req_delete": "package req\n\ntype DeleteWarehouseReqDto struct {\n\tId *string `json:\"id\"` // 仓库ID\n}\n", "req_query": "package req\n\ntype QueryWarehouseReqDto struct {\n\tId                    *string `json:\"id\"`                    // 仓库ID\n\tName                  *string `json:\"name\"`                  // 仓库名称\n\tRemark               *string `json:\"remark\"`               // 备注\n\tIsDefaultLiquorStorage *bool   `json:\"isDefaultLiquorStorage\"` // 是否为默认酒水仓库\n\tPageNum              *int    `json:\"pageNum\"`              // 页码\n\tPageSize             *int    `json:\"pageSize\"`             // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype WarehouseTransfer struct {\n}\n\nfunc (transfer *WarehouseTransfer) PoToVo(po po.Warehouse) vo.WarehouseVO {\n\tvo := vo.WarehouseVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *WarehouseTransfer) VoToPo(vo vo.WarehouseVO) po.Warehouse {\n\tpo := po.Warehouse{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype WarehouseService struct {\n}\n\nfunc (service *WarehouseService) CreateWarehouse(logCtx *gin.Context, warehouse *po.Warehouse) error {\n\treturn Save(warehouse)\n}\n\nfunc (service *WarehouseService) UpdateWarehouse(logCtx *gin.Context, warehouse *po.Warehouse) error {\n\treturn Update(warehouse)\n}\n\nfunc (service *WarehouseService) DeleteWarehouse(logCtx *gin.Context, id string) error {\n\treturn Delete(po.Warehouse{Id: &id})\n}\n\nfunc (service *WarehouseService) FindWarehouseById(logCtx *gin.Context, id string) (warehouse *po.Warehouse, err error) {\n\twarehouse = &po.Warehouse{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(warehouse).Error\n\treturn\n}\n\nfunc (service *WarehouseService) FindAllWarehouse(logCtx *gin.Context, reqDto *req.QueryWarehouseReqDto) (list *[]po.Warehouse, err error) {\n\tdb := model.DBSlave.Self.Model(&po.Warehouse{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name LIKE ?\", \"%\"+*reqDto.Name+\"%\")\n\t}\n\tif reqDto.Remark != nil && *reqDto.Remark != \"\" {\n\t\tdb = db.Where(\"remark LIKE ?\", \"%\"+*reqDto.Remark+\"%\")\n\t}\n\tif reqDto.IsDefaultLiquorStorage != nil {\n\t\tdb = db.Where(\"is_default_liquor_storage=?\", *reqDto.IsDefaultLiquorStorage)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.Warehouse{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *WarehouseService) FindAllWarehouseWithPagination(logCtx *gin.Context, reqDto *req.QueryWarehouseReqDto) (list *[]po.Warehouse, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.Warehouse{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name LIKE ?\", \"%\"+*reqDto.Name+\"%\")\n\t}\n\tif reqDto.Remark != nil && *reqDto.Remark != \"\" {\n\t\tdb = db.Where(\"remark LIKE ?\", \"%\"+*reqDto.Remark+\"%\")\n\t}\n\tif reqDto.IsDefaultLiquorStorage != nil {\n\t\tdb = db.Where(\"is_default_liquor_storage=?\", *reqDto.IsDefaultLiquorStorage)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.Warehouse{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype WarehouseController struct{}\n\nvar (\n\twarehouseService  = impl.WarehouseService{}\n\twarehouseTransfer = transfer.WarehouseTransfer{}\n)\n\n// @Summary 添加仓库\n// @Description 添加仓库\n// @Tags 仓库\n// @Accept json\n// @Produce json\n// @Param body body req.AddWarehouseReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.WarehouseVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/warehouse/add [post]\nfunc (controller *WarehouseController) AddWarehouse(ctx *gin.Context) {\n\treqDto := req.AddWarehouseReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\twarehouse := po.Warehouse{}\n\tif reqDto.Name != nil {\n\t\twarehouse.Name = reqDto.Name\n\t}\n\tif reqDto.Remark != nil {\n\t\twarehouse.Remark = reqDto.Remark\n\t}\n\tif reqDto.IsDefaultLiquorStorage != nil {\n\t\twarehouse.IsDefaultLiquorStorage = reqDto.IsDefaultLiquorStorage\n\t}\n\n\terr = warehouseService.CreateWarehouse(ctx, &warehouse)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, warehouseTransfer.PoToVo(warehouse))\n}\n\n// @Summary 更新仓库\n// @Description 更新仓库\n// @Tags 仓库\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateWarehouseReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.WarehouseVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/warehouse/update [post]\nfunc (controller *WarehouseController) UpdateWarehouse(ctx *gin.Context) {\n\treqDto := req.UpdateWarehouseReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\n\twarehouse, err := warehouseService.FindWarehouseById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tif reqDto.Name != nil {\n\t\twarehouse.Name = reqDto.Name\n\t}\n\tif reqDto.Remark != nil {\n\t\twarehouse.Remark = reqDto.Remark\n\t}\n\tif reqDto.IsDefaultLiquorStorage != nil {\n\t\twarehouse.IsDefaultLiquorStorage = reqDto.IsDefaultLiquorStorage\n\t}\n\n\terr = warehouseService.UpdateWarehouse(ctx, warehouse)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, warehouseTransfer.PoToVo(*warehouse))\n}\n\n// @Summary 删除仓库\n// @Description 删除仓库\n// @Tags 仓库\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteWarehouseReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/warehouse/delete [post]\nfunc (controller *WarehouseController) DeleteWarehouse(ctx *gin.Context) {\n\treqDto := req.DeleteWarehouseReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\terr = warehouseService.DeleteWarehouse(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询仓库\n// @Description 查询仓库\n// @Tags 仓库\n// @Accept json\n// @Produce json\n// @Param body body req.QueryWarehouseReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.WarehouseVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/warehouse/query [post]\nfunc (controller *WarehouseController) QueryWarehouses(ctx *gin.Context) {\n\treqDto := req.QueryWarehouseReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tlist, err := warehouseService.FindAllWarehouse(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tnewList := []vo.WarehouseVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, warehouseTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询仓库列表\n// @Description 查询仓库列表\n// @Tags 仓库\n// @Accept json\n// @Produce json\n// @Param body body req.QueryWarehouseReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.WarehouseVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/warehouse/list [post]\nfunc (controller *WarehouseController) ListWarehouses(ctx *gin.Context) {\n\treqDto := req.QueryWarehouseReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\n\tlist, totalCount, err := warehouseService.FindAllWarehouseWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tpage := vo.PageVO[[]vo.WarehouseVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.WarehouseVO{}\n\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, warehouseTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype WarehouseRoute struct {\n}\n\nfunc (s *WarehouseRoute) InitWarehouseRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\twarehouseController := controller.WarehouseController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/warehouse/add\", warehouseController.AddWarehouse)       // add\n\t\troute.POST(\"/api/warehouse/update\", warehouseController.UpdateWarehouse) // update\n\t\troute.POST(\"/api/warehouse/delete\", warehouseController.DeleteWarehouse) // delete\n\t\troute.POST(\"/api/warehouse/query\", warehouseController.QueryWarehouses)  // query\n\t\troute.POST(\"/api/warehouse/list\", warehouseController.ListWarehouses)   // list\n\t}\n}\n"}]