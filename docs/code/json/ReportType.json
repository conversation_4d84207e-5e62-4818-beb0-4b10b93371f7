[{"po": "package po\n\n// ReportType 报表类型实体\ntype ReportType struct {\n\tId          *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`          // 唯一id\n\tName        *string `gorm:\"column:name;type:varchar(255);default:''\" json:\"name\"`        // 报表类型名称\n\tDescription *string `gorm:\"column:description;type:varchar(255);default:''\" json:\"description\"` // 描述\n\tFrequency   *string `gorm:\"column:frequency;type:varchar(64);default:''\" json:\"frequency\"`   // 频率\n\tCtime       *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                   // 创建时间\n\tUtime       *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                   // 更新时间\n\tState       *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                   // 状态\n\tVersion     *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`               // 版本号\n}\n\n// TableName 设置表名\nfunc (ReportType) TableName() string {\n\treturn \"report_type\"\n}\n\nfunc (r ReportType) GetId() string {\n\treturn *r.Id\n}\n", "vo": "package vo\n\n// ReportTypeVO 报表类型值对象\ntype ReportTypeVO struct {\n\tId          string `json:\"id\"`          // 唯一id\n\tName        string `json:\"name\"`        // 报表类型名称\n\tDescription string `json:\"description\"` // 描述\n\tFrequency   string `json:\"frequency\"`   // 频率\n\tCtime       int64  `json:\"ctime\"`       // 创建时间\n\tUtime       int64  `json:\"utime\"`       // 更新时间\n\tState       int    `json:\"state\"`       // 状态\n\tVersion     int    `json:\"version\"`     // 版本号\n}\n", "req_add": "package req\n\n// AddReportTypeReqDto 创建报表类型请求DTO\ntype AddReportTypeReqDto struct {\n\tName        *string `json:\"name\"`        // 报表类型名称\n\tDescription *string `json:\"description\"` // 描述\n\tFrequency   *string `json:\"frequency\"`   // 频率\n}\n", "req_update": "package req\n\ntype UpdateReportTypeReqDto struct {\n\tId          *string `json:\"id\"`          // 唯一id\n\tName        *string `json:\"name\"`        // 报表类型名称\n\tDescription *string `json:\"description\"` // 描述\n\tFrequency   *string `json:\"frequency\"`   // 频率\n}\n", "req_delete": "package req\n\ntype DeleteReportTypeReqDto struct {\n\tId *string `json:\"id\"` // 唯一id\n}\n", "req_query": "package req\n\ntype QueryReportTypeReqDto struct {\n\tId          *string `json:\"id\"`          // 唯一id\n\tName        *string `json:\"name\"`        // 报表类型名称\n\tDescription *string `json:\"description\"` // 描述\n\tFrequency   *string `json:\"frequency\"`   // 频率\n\tPageNum     *int    `json:\"pageNum\"`     // 页码\n\tPageSize    *int    `json:\"pageSize\"`    // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype ReportTypeTransfer struct {\n}\n\nfunc (transfer *ReportTypeTransfer) PoToVo(po po.ReportType) vo.ReportTypeVO {\n\tvo := vo.ReportTypeVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *ReportTypeTransfer) VoToPo(vo vo.ReportTypeVO) po.ReportType {\n\tpo := po.ReportType{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype ReportTypeService struct {\n}\n\nfunc (service *ReportTypeService) CreateReportType(logCtx *gin.Context, reportType *po.ReportType) error {\n\treturn Save(reportType)\n}\n\nfunc (service *ReportTypeService) UpdateReportType(logCtx *gin.Context, reportType *po.ReportType) error {\n\treturn Update(reportType)\n}\n\nfunc (service *ReportTypeService) DeleteReportType(logCtx *gin.Context, id string) error {\n\treturn Delete(po.ReportType{Id: &id})\n}\n\nfunc (service *ReportTypeService) FindReportTypeById(logCtx *gin.Context, id string) (reportType *po.ReportType, err error) {\n\treportType = &po.ReportType{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(reportType).Error\n\treturn\n}\n\nfunc (service *ReportTypeService) FindAllReportType(logCtx *gin.Context, reqDto *req.QueryReportTypeReqDto) (list *[]po.ReportType, err error) {\n\tdb := model.DBSlave.Self.Model(&po.ReportType{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name LIKE ?\", \"%\"+*reqDto.Name+\"%\")\n\t}\n\tif reqDto.Description != nil && *reqDto.Description != \"\" {\n\t\tdb = db.Where(\"description LIKE ?\", \"%\"+*reqDto.Description+\"%\")\n\t}\n\tif reqDto.Frequency != nil && *reqDto.Frequency != \"\" {\n\t\tdb = db.Where(\"frequency=?\", *reqDto.Frequency)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.ReportType{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *ReportTypeService) FindAllReportTypeWithPagination(logCtx *gin.Context, reqDto *req.QueryReportTypeReqDto) (list *[]po.ReportType, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.ReportType{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name LIKE ?\", \"%\"+*reqDto.Name+\"%\")\n\t}\n\tif reqDto.Description != nil && *reqDto.Description != \"\" {\n\t\tdb = db.Where(\"description LIKE ?\", \"%\"+*reqDto.Description+\"%\")\n\t}\n\tif reqDto.Frequency != nil && *reqDto.Frequency != \"\" {\n\t\tdb = db.Where(\"frequency=?\", *reqDto.Frequency)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.ReportType{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype ReportTypeController struct{}\n\nvar (\n\treportTypeService  = impl.ReportTypeService{}\n\treportTypeTransfer = transfer.ReportTypeTransfer{}\n)\n\n// @Summary 添加报表类型\n// @Description 添加报表类型\n// @Tags 报表类型\n// @Accept json\n// @Produce json\n// @Param body body req.AddReportTypeReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.ReportTypeVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/report-type/add [post]\nfunc (controller *ReportTypeController) AddReportType(ctx *gin.Context) {\n\treqDto := req.AddReportTypeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\treportType := po.ReportType{}\n\tif reqDto.Name != nil {\n\t\treportType.Name = reqDto.Name\n\t}\n\tif reqDto.Description != nil {\n\t\treportType.Description = reqDto.Description\n\t}\n\tif reqDto.Frequency != nil {\n\t\treportType.Frequency = reqDto.Frequency\n\t}\n\terr = reportTypeService.CreateReportType(ctx, &reportType)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, reportTypeTransfer.PoToVo(reportType))\n}\n\n// @Summary 更新报表类型\n// @Description 更新报表类型\n// @Tags 报表类型\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateReportTypeReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.ReportTypeVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/report-type/update [post]\nfunc (controller *ReportTypeController) UpdateReportType(ctx *gin.Context) {\n\treqDto := req.UpdateReportTypeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\treportType, err := reportTypeService.FindReportTypeById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Name != nil {\n\t\treportType.Name = reqDto.Name\n\t}\n\tif reqDto.Description != nil {\n\t\treportType.Description = reqDto.Description\n\t}\n\tif reqDto.Frequency != nil {\n\t\treportType.Frequency = reqDto.Frequency\n\t}\n\terr = reportTypeService.UpdateReportType(ctx, reportType)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, reportTypeTransfer.PoToVo(*reportType))\n}\n\n// @Summary 删除报表类型\n// @Description 删除报表类型\n// @Tags 报表类型\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteReportTypeReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/report-type/delete [post]\nfunc (controller *ReportTypeController) DeleteReportType(ctx *gin.Context) {\n\treqDto := req.DeleteReportTypeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = reportTypeService.DeleteReportType(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询报表类型\n// @Description 查询报表类型\n// @Tags 报表类型\n// @Accept json\n// @Produce json\n// @Param body body req.QueryReportTypeReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.ReportTypeVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/report-type/query [post]\nfunc (controller *ReportTypeController) QueryReportTypes(ctx *gin.Context) {\n\treqDto := req.QueryReportTypeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := reportTypeService.FindAllReportType(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.ReportTypeVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, reportTypeTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询报表类型列表\n// @Description 查询报表类型列表\n// @Tags 报表类型\n// @Accept json\n// @Produce json\n// @Param body body req.QueryReportTypeReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.ReportTypeVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/report-type/list [post]\nfunc (a *ReportTypeController) ListReportTypes(ctx *gin.Context) {\n\treqDto := req.QueryReportTypeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := reportTypeService.FindAllReportTypeWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.ReportTypeVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.ReportTypeVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, reportTypeTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype ReportTypeRoute struct {\n}\n\nfunc (s *ReportTypeRoute) InitReportTypeRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\treportTypeController := controller.ReportTypeController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/report-type/add\", reportTypeController.AddReportType)    //add\n\t\troute.POST(\"/api/report-type/update\", reportTypeController.UpdateReportType) //update\n\t\troute.POST(\"/api/report-type/delete\", reportTypeController.DeleteReportType) //delete\n\t\troute.POST(\"/api/report-type/query\", reportTypeController.QueryReportTypes)     //query\n\t\troute.POST(\"/api/report-type/list\", reportTypeController.ListReportTypes)     //list\n\t}\n}\n"}]