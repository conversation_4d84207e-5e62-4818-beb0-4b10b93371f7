[{"po": "package po\n\n// Room 房间实体\ntype Room struct {\n\tId                  *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`                // ID\n\tVenueId             *string `gorm:\"column:venue_id;type:varchar(64);default:''\" json:\"venueId\"`           // 所属门店ID\n\tSessionId           *string `gorm:\"column:session_id;type:varchar(64);default:''\" json:\"sessionId\"`       // 场次ID\n\tName                *string `gorm:\"column:name;type:varchar(255);default:''\" json:\"name\"`                 // 房间名称\n\tTypeId              *string `gorm:\"column:type_id;type:varchar(64);default:''\" json:\"typeId\"`             // 房间类型ID\n\tAreaId              *string `gorm:\"column:area_id;type:varchar(64);default:''\" json:\"areaId\"`             // 区域ID\n\tThemeId             *string `gorm:\"column:theme_id;type:varchar(64);default:''\" json:\"themeId\"`           // 主题ID\n\tPricePlanId         *string `gorm:\"column:price_plan_id;type:varchar(64);default:''\" json:\"pricePlanId\"` // 价格方案ID\n\tHighConsumptionAlert *int64  `gorm:\"column:high_consumption_alert;type:int;default:0\" json:\"highConsumptionAlert\"` // 高消费警报阈值\n\tStatus              *string `gorm:\"column:status;type:varchar(64);default:''\" json:\"status\"`               // 房间状态\n\tOpenTime            *int64  `gorm:\"column:open_time;type:int;default:0\" json:\"openTime\"`                   // 开放时间\n\tCloseTime           *int64  `gorm:\"column:close_time;type:int;default:0\" json:\"closeTime\"`                 // 关闭时间\n\tConsumptionMode     *string `gorm:\"column:consumption_mode;type:varchar(64);default:''\" json:\"consumptionMode\"` // 消费模式\n\tInteriorPhoto       *string `gorm:\"column:interior_photo;type:varchar(255);default:''\" json:\"interiorPhoto\"` // 室内照片\n\tIsDisplayed         *bool   `gorm:\"column:is_displayed;type:tinyint(1);default:0\" json:\"isDisplayed\"`     // 是否显示\n\tQrCode              *string `gorm:\"column:qr_code;type:varchar(255);default:''\" json:\"qrCode\"`             // 二维码\n\tColor               *string `gorm:\"column:color;type:varchar(64);default:''\" json:\"color\"`                 // 颜色\n\tDisplayItems        *string `gorm:\"column:display_items;type:varchar(255);default:''\" json:\"displayItems\"` // 显示项目\n\tCtime               *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                         // 创建时间\n\tUtime               *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                         // 更新时间\n\tState               *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                         // 状态\n\tVersion             *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`                     // 版本\n}\n\n// TableName 设置表名\nfunc (Room) TableName() string {\n\treturn \"room\"\n}\n\nfunc (r Room) GetId() string {\n\treturn *r.Id\n}\n", "vo": "package vo\n\n// RoomVO 房间信息值对象\ntype RoomVO struct {\n\tId                  string `json:\"id\"`                  // ID\n\tVenueId             string `json:\"venueId\"`             // 所属门店ID\n\tSessionId           string `json:\"sessionId\"`           // 场次ID\n\tName                string `json:\"name\"`                // 房间名称\n\tTypeId              string `json:\"typeId\"`              // 房间类型ID\n\tAreaId              string `json:\"areaId\"`              // 区域ID\n\tThemeId             string `json:\"themeId\"`             // 主题ID\n\tPricePlanId         string `json:\"pricePlanId\"`         // 价格方案ID\n\tHighConsumptionAlert int64  `json:\"highConsumptionAlert\"` // 高消费警报阈值\n\tStatus              string `json:\"status\"`              // 房间状态\n\tOpenTime            int64  `json:\"openTime\"`            // 开放时间\n\tCloseTime           int64  `json:\"closeTime\"`           // 关闭时间\n\tConsumptionMode     string `json:\"consumptionMode\"`     // 消费模式\n\tInteriorPhoto       string `json:\"interiorPhoto\"`       // 室内照片\n\tIsDisplayed         bool   `json:\"isDisplayed\"`         // 是否显示\n\tQrCode              string `json:\"qrCode\"`              // 二维码\n\tColor               string `json:\"color\"`               // 颜色\n\tDisplayItems        string `json:\"displayItems\"`        // 显示项目\n\tCtime               int64  `json:\"ctime\"`               // 创建时间\n\tUtime               int64  `json:\"utime\"`               // 更新时间\n\tState               int    `json:\"state\"`               // 状态\n\tVersion             int    `json:\"version\"`             // 版本\n}\n", "req_add": "package req\n\n// AddRoomReqDto 创建房间请求DTO\ntype AddRoomReqDto struct {\n\tVenueId             *string `json:\"venueId\"`             // 所属门店ID\n\tSessionId           *string `json:\"sessionId\"`           // 场次ID\n\tName                *string `json:\"name\"`                // 房间名称\n\tTypeId              *string `json:\"typeId\"`              // 房间类型ID\n\tAreaId              *string `json:\"areaId\"`              // 区域ID\n\tThemeId             *string `json:\"themeId\"`             // 主题ID\n\tPricePlanId         *string `json:\"pricePlanId\"`         // 价格方案ID\n\tHighConsumptionAlert *int64  `json:\"highConsumptionAlert\"` // 高消费警报阈值\n\tStatus              *string `json:\"status\"`              // 房间状态\n\tOpenTime            *int64  `json:\"openTime\"`            // 开放时间\n\tCloseTime           *int64  `json:\"closeTime\"`           // 关闭时间\n\tConsumptionMode     *string `json:\"consumptionMode\"`     // 消费模式\n\tInteriorPhoto       *string `json:\"interiorPhoto\"`       // 室内照片\n\tIsDisplayed         *bool   `json:\"isDisplayed\"`         // 是否显示\n\tQrCode              *string `json:\"qrCode\"`              // 二维码\n\tColor               *string `json:\"color\"`               // 颜色\n\tDisplayItems        *string `json:\"displayItems\"`        // 显示项目\n}\n", "req_update": "package req\n\n// UpdateRoomReqDto 更新房间请求DTO\ntype UpdateRoomReqDto struct {\n\tId                  *string `json:\"id\"`                  // ID\n\tVenueId             *string `json:\"venueId\"`             // 所属门店ID\n\tSessionId           *string `json:\"sessionId\"`           // 场次ID\n\tName                *string `json:\"name\"`                // 房间名称\n\tTypeId              *string `json:\"typeId\"`              // 房间类型ID\n\tAreaId              *string `json:\"areaId\"`              // 区域ID\n\tThemeId             *string `json:\"themeId\"`             // 主题ID\n\tPricePlanId         *string `json:\"pricePlanId\"`         // 价格方案ID\n\tHighConsumptionAlert *int64  `json:\"highConsumptionAlert\"` // 高消费警报阈值\n\tStatus              *string `json:\"status\"`              // 房间状态\n\tOpenTime            *int64  `json:\"openTime\"`            // 开放时间\n\tCloseTime           *int64  `json:\"closeTime\"`           // 关闭时间\n\tConsumptionMode     *string `json:\"consumptionMode\"`     // 消费模式\n\tInteriorPhoto       *string `json:\"interiorPhoto\"`       // 室内照片\n\tIsDisplayed         *bool   `json:\"isDisplayed\"`         // 是否显示\n\tQrCode              *string `json:\"qrCode\"`              // 二维码\n\tColor               *string `json:\"color\"`               // 颜色\n\tDisplayItems        *string `json:\"displayItems\"`        // 显示项目\n}\n", "req_delete": "package req\n\ntype DeleteRoomReqDto struct {\n\tId *string `json:\"id\"` // ID\n}\n", "req_query": "package req\n\ntype QueryRoomReqDto struct {\n\tId                  *string `json:\"id\"`                  // ID\n\tVenueId             *string `json:\"venueId\"`             // 所属门店ID\n\tSessionId           *string `json:\"sessionId\"`           // 场次ID\n\tName                *string `json:\"name\"`                // 房间名称\n\tTypeId              *string `json:\"typeId\"`              // 房间类型ID\n\tAreaId              *string `json:\"areaId\"`              // 区域ID\n\tThemeId             *string `json:\"themeId\"`             // 主题ID\n\tPricePlanId         *string `json:\"pricePlanId\"`         // 价格方案ID\n\tStatus              *string `json:\"status\"`              // 房间状态\n\tConsumptionMode     *string `json:\"consumptionMode\"`     // 消费模式\n\tIsDisplayed         *bool   `json:\"isDisplayed\"`         // 是否显示\n\tPageNum             *int    `json:\"pageNum\"`             // 页码\n\tPageSize            *int    `json:\"pageSize\"`            // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype RoomTransfer struct {\n}\n\nfunc (transfer *RoomTransfer) PoToVo(po po.Room) vo.RoomVO {\n\tvo := vo.RoomVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *RoomTransfer) VoToPo(vo vo.RoomVO) po.Room {\n\tpo := po.Room{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype RoomService struct {\n}\n\nfunc (service *RoomService) CreateRoom(logCtx *gin.Context, room *po.Room) error {\n\treturn Save(room)\n}\n\nfunc (service *RoomService) UpdateRoom(logCtx *gin.Context, room *po.Room) error {\n\treturn Update(room)\n}\n\nfunc (service *RoomService) DeleteRoom(logCtx *gin.Context, id string) error {\n\treturn Delete(po.Room{Id: &id})\n}\n\nfunc (service *RoomService) FindRoomById(logCtx *gin.Context, id string) (room *po.Room, err error) {\n\troom = &po.Room{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(room).Error\n\treturn\n}\n\nfunc (service *RoomService) FindAllRoom(logCtx *gin.Context, reqDto *req.QueryRoomReqDto) (list *[]po.Room, err error) {\n\tdb := model.DBSlave.Self.Model(&po.Room{})\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.VenueId != nil && *reqDto.VenueId != \"\" {\n\t\tdb = db.Where(\"venue_id=?\", *reqDto.VenueId)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name LIKE ?\", \"%\"+*reqDto.Name+\"%\")\n\t}\n\tif reqDto.TypeId != nil && *reqDto.TypeId != \"\" {\n\t\tdb = db.Where(\"type_id=?\", *reqDto.TypeId)\n\t}\n\tif reqDto.AreaId != nil && *reqDto.AreaId != \"\" {\n\t\tdb = db.Where(\"area_id=?\", *reqDto.AreaId)\n\t}\n\tif reqDto.Status != nil && *reqDto.Status != \"\" {\n\t\tdb = db.Where(\"status=?\", *reqDto.Status)\n\t}\n\tif reqDto.IsDisplayed != nil {\n\t\tdb = db.Where(\"is_displayed=?\", *reqDto.IsDisplayed)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.Room{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *RoomService) FindAllRoomWithPagination(logCtx *gin.Context, reqDto *req.QueryRoomReqDto) (list *[]po.Room, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.Room{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.VenueId != nil && *reqDto.VenueId != \"\" {\n\t\tdb = db.Where(\"venue_id=?\", *reqDto.VenueId)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name LIKE ?\", \"%\"+*reqDto.Name+\"%\")\n\t}\n\tif reqDto.TypeId != nil && *reqDto.TypeId != \"\" {\n\t\tdb = db.Where(\"type_id=?\", *reqDto.TypeId)\n\t}\n\tif reqDto.AreaId != nil && *reqDto.AreaId != \"\" {\n\t\tdb = db.Where(\"area_id=?\", *reqDto.AreaId)\n\t}\n\tif reqDto.Status != nil && *reqDto.Status != \"\" {\n\t\tdb = db.Where(\"status=?\", *reqDto.Status)\n\t}\n\tif reqDto.IsDisplayed != nil {\n\t\tdb = db.Where(\"is_displayed=?\", *reqDto.IsDisplayed)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.Room{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype RoomController struct{}\n\nvar (\n\troomService  = impl.RoomService{}\n\troomTransfer = transfer.RoomTransfer{}\n)\n\n// @Summary 添加房间\n// @Description 添加房间\n// @Tags 房间\n// @Accept json\n// @Produce json\n// @Param body body req.AddRoomReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.RoomVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/room/add [post]\nfunc (controller *RoomController) AddRoom(ctx *gin.Context) {\n\treqDto := req.AddRoomReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\troom := po.Room{}\n\tif reqDto.VenueId != nil {\n\t\troom.VenueId = reqDto.VenueId\n\t}\n\tif reqDto.SessionId != nil {\n\t\troom.SessionId = reqDto.SessionId\n\t}\n\tif reqDto.Name != nil {\n\t\troom.Name = reqDto.Name\n\t}\n\tif reqDto.TypeId != nil {\n\t\troom.TypeId = reqDto.TypeId\n\t}\n\tif reqDto.AreaId != nil {\n\t\troom.AreaId = reqDto.AreaId\n\t}\n\tif reqDto.ThemeId != nil {\n\t\troom.ThemeId = reqDto.ThemeId\n\t}\n\tif reqDto.PricePlanId != nil {\n\t\troom.PricePlanId = reqDto.PricePlanId\n\t}\n\tif reqDto.HighConsumptionAlert != nil {\n\t\troom.HighConsumptionAlert = reqDto.HighConsumptionAlert\n\t}\n\tif reqDto.Status != nil {\n\t\troom.Status = reqDto.Status\n\t}\n\tif reqDto.OpenTime != nil {\n\t\troom.OpenTime = reqDto.OpenTime\n\t}\n\tif reqDto.CloseTime != nil {\n\t\troom.CloseTime = reqDto.CloseTime\n\t}\n\tif reqDto.ConsumptionMode != nil {\n\t\troom.ConsumptionMode = reqDto.ConsumptionMode\n\t}\n\tif reqDto.InteriorPhoto != nil {\n\t\troom.InteriorPhoto = reqDto.InteriorPhoto\n\t}\n\tif reqDto.IsDisplayed != nil {\n\t\troom.IsDisplayed = reqDto.IsDisplayed\n\t}\n\tif reqDto.QrCode != nil {\n\t\troom.QrCode = reqDto.QrCode\n\t}\n\tif reqDto.Color != nil {\n\t\troom.Color = reqDto.Color\n\t}\n\tif reqDto.DisplayItems != nil {\n\t\troom.DisplayItems = reqDto.DisplayItems\n\t}\n\n\terr = roomService.CreateRoom(ctx, &room)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, roomTransfer.PoToVo(room))\n}\n\n// @Summary 更新房间\n// @Description 更新房间\n// @Tags 房间\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateRoomReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.RoomVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/room/update [post]\nfunc (controller *RoomController) UpdateRoom(ctx *gin.Context) {\n\treqDto := req.UpdateRoomReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\n\troom, err := roomService.FindRoomById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tif reqDto.VenueId != nil {\n\t\troom.VenueId = reqDto.VenueId\n\t}\n\tif reqDto.SessionId != nil {\n\t\troom.SessionId = reqDto.SessionId\n\t}\n\tif reqDto.Name != nil {\n\t\troom.Name = reqDto.Name\n\t}\n\tif reqDto.TypeId != nil {\n\t\troom.TypeId = reqDto.TypeId\n\t}\n\tif reqDto.AreaId != nil {\n\t\troom.AreaId = reqDto.AreaId\n\t}\n\tif reqDto.ThemeId != nil {\n\t\troom.ThemeId = reqDto.ThemeId\n\t}\n\tif reqDto.PricePlanId != nil {\n\t\troom.PricePlanId = reqDto.PricePlanId\n\t}\n\tif reqDto.HighConsumptionAlert != nil {\n\t\troom.HighConsumptionAlert = reqDto.HighConsumptionAlert\n\t}\n\tif reqDto.Status != nil {\n\t\troom.Status = reqDto.Status\n\t}\n\tif reqDto.OpenTime != nil {\n\t\troom.OpenTime = reqDto.OpenTime\n\t}\n\tif reqDto.CloseTime != nil {\n\t\troom.CloseTime = reqDto.CloseTime\n\t}\n\tif reqDto.ConsumptionMode != nil {\n\t\troom.ConsumptionMode = reqDto.ConsumptionMode\n\t}\n\tif reqDto.InteriorPhoto != nil {\n\t\troom.InteriorPhoto = reqDto.InteriorPhoto\n\t}\n\tif reqDto.IsDisplayed != nil {\n\t\troom.IsDisplayed = reqDto.IsDisplayed\n\t}\n\tif reqDto.QrCode != nil {\n\t\troom.QrCode = reqDto.QrCode\n\t}\n\tif reqDto.Color != nil {\n\t\troom.Color = reqDto.Color\n\t}\n\tif reqDto.DisplayItems != nil {\n\t\troom.DisplayItems = reqDto.DisplayItems\n\t}\n\n\terr = roomService.UpdateRoom(ctx, room)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, roomTransfer.PoToVo(*room))\n}\n\n// @Summary 删除房间\n// @Description 删除房间\n// @Tags 房间\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteRoomReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/room/delete [post]\nfunc (controller *RoomController) DeleteRoom(ctx *gin.Context) {\n\treqDto := req.DeleteRoomReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = roomService.DeleteRoom(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询房间\n// @Description 查询房间\n// @Tags 房间\n// @Accept json\n// @Produce json\n// @Param body body req.QueryRoomReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.RoomVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/room/query [post]\nfunc (controller *RoomController) QueryRooms(ctx *gin.Context) {\n\treqDto := req.QueryRoomReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := roomService.FindAllRoom(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.RoomVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, roomTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询房间列表\n// @Description 查询房间列表\n// @Tags 房间\n// @Accept json\n// @Produce json\n// @Param body body req.QueryRoomReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.RoomVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/room/list [post]\nfunc (controller *RoomController) ListRooms(ctx *gin.Context) {\n\treqDto := req.QueryRoomReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := roomService.FindAllRoomWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.RoomVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.RoomVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, roomTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype RoomRoute struct {\n}\n\nfunc (s *RoomRoute) InitRoomRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\troomController := controller.RoomController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/room/add\", roomController.AddRoom)       // add\n\t\troute.POST(\"/api/room/update\", roomController.UpdateRoom) // update\n\t\troute.POST(\"/api/room/delete\", roomController.DeleteRoom) // delete\n\t\troute.POST(\"/api/room/query\", roomController.QueryRooms)  // query\n\t\troute.POST(\"/api/room/list\", roomController.ListRooms)   // list\n\t}\n}\n"}]