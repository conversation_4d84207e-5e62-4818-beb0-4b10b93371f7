[{"po": "package po\n\n// SingingDevice 唱歌设备实体\ntype SingingDevice struct {\n\tId        *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`          // ID\n\tType      *string `gorm:\"column:type;type:varchar(64);default:''\" json:\"type\"`            // 设备类型\n\tIpAddress *string `gorm:\"column:ip_address;type:varchar(64);default:''\" json:\"ipAddress\"`  // IP地址\n\tCtime     *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                   // 创建时间戳\n\tUtime     *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                   // 更新时间戳\n\tState     *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                   // 状态值\n\tVersion   *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`               // 版本号\n}\n\n// TableName 设置表名\nfunc (SingingDevice) TableName() string {\n\treturn \"singing_device\"\n}\n\nfunc (s SingingDevice) GetId() string {\n\treturn *s.Id\n}", "req_add": "package req\n\n// AddSingingDeviceReqDto 创建唱歌设备请求DTO\ntype AddSingingDeviceReqDto struct {\n\tType      *string `json:\"type\"`      // 设备类型\n\tIpAddress *string `json:\"ipAddress\"` // IP地址\n}", "req_update": "package req\n\ntype UpdateSingingDeviceReqDto struct {\n\tId        *string `json:\"id\"`        // 设备ID\n\tType      *string `json:\"type\"`      // 设备类型\n\tIpAddress *string `json:\"ipAddress\"` // IP地址\n}", "req_delete": "package req\n\ntype DeleteSingingDeviceReqDto struct {\n\tId *string `json:\"id\"` // 设备ID\n}", "req_query": "package req\n\ntype QuerySingingDeviceReqDto struct {\n\tId        *string `json:\"id\"`        // 设备ID\n\tType      *string `json:\"type\"`      // 设备类型\n\tIpAddress *string `json:\"ipAddress\"` // IP地址\n\tPageNum   *int    `json:\"pageNum\"`   // 页码\n\tPageSize  *int    `json:\"pageSize\"`  // 每页记录数\n}", "vo": "package vo\n\n// SingingDeviceVO 唱歌设备值对象\ntype SingingDeviceVO struct {\n\tId        string `json:\"id\"`        // ID\n\tType      string `json:\"type\"`      // 设备类型\n\tIpAddress string `json:\"ipAddress\"` // IP地址\n\tCtime     int64  `json:\"ctime\"`     // 创建时间戳\n\tUtime     int64  `json:\"utime\"`     // 更新时间戳\n\tState     int    `json:\"state\"`     // 状态值\n\tVersion   int    `json:\"version\"`   // 版本号\n}", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype SingingDeviceTransfer struct {\n}\n\nfunc (transfer *SingingDeviceTransfer) PoToVo(po po.SingingDevice) vo.SingingDeviceVO {\n\tvo := vo.SingingDeviceVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *SingingDeviceTransfer) VoToPo(vo vo.SingingDeviceVO) po.SingingDevice {\n\tpo := po.SingingDevice{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype SingingDeviceService struct {\n}\n\nfunc (service *SingingDeviceService) CreateSingingDevice(logCtx *gin.Context, device *po.SingingDevice) error {\n\treturn Save(device)\n}\n\nfunc (service *SingingDeviceService) UpdateSingingDevice(logCtx *gin.Context, device *po.SingingDevice) error {\n\treturn Update(device)\n}\n\nfunc (service *SingingDeviceService) DeleteSingingDevice(logCtx *gin.Context, id string) error {\n\treturn Delete(po.SingingDevice{Id: &id})\n}\n\nfunc (service *SingingDeviceService) FindSingingDeviceById(logCtx *gin.Context, id string) (device *po.SingingDevice, err error) {\n\tdevice = &po.SingingDevice{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(device).Error\n\treturn\n}\n\nfunc (service *SingingDeviceService) FindAllSingingDevice(logCtx *gin.Context, reqDto *req.QuerySingingDeviceReqDto) (list *[]po.SingingDevice, err error) {\n\tdb := model.DBSlave.Self.Model(&po.SingingDevice{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Type != nil && *reqDto.Type != \"\" {\n\t\tdb = db.Where(\"type=?\", *reqDto.Type)\n\t}\n\tif reqDto.IpAddress != nil && *reqDto.IpAddress != \"\" {\n\t\tdb = db.Where(\"ip_address=?\", *reqDto.IpAddress)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.SingingDevice{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *SingingDeviceService) FindAllSingingDeviceWithPagination(logCtx *gin.Context, reqDto *req.QuerySingingDeviceReqDto) (list *[]po.SingingDevice, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.SingingDevice{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Type != nil && *reqDto.Type != \"\" {\n\t\tdb = db.Where(\"type=?\", *reqDto.Type)\n\t}\n\tif reqDto.IpAddress != nil && *reqDto.IpAddress != \"\" {\n\t\tdb = db.Where(\"ip_address=?\", *reqDto.IpAddress)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.SingingDevice{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype SingingDeviceController struct{}\n\nvar (\n\tsingingDeviceService  = impl.SingingDeviceService{}\n\tsingingDeviceTransfer = transfer.SingingDeviceTransfer{}\n)\n\n// @Summary 添加唱歌设备\n// @Description 添加唱歌设备\n// @Tags 唱歌设备\n// @Accept json\n// @Produce json\n// @Param body body req.AddSingingDeviceReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.SingingDeviceVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/singing-device/add [post]\nfunc (controller *SingingDeviceController) AddSingingDevice(ctx *gin.Context) {\n\treqDto := req.AddSingingDeviceReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tdevice := po.SingingDevice{}\n\tif reqDto.Type != nil {\n\t\tdevice.Type = reqDto.Type\n\t}\n\tif reqDto.IpAddress != nil {\n\t\tdevice.IpAddress = reqDto.IpAddress\n\t}\n\terr = singingDeviceService.CreateSingingDevice(ctx, &device)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, singingDeviceTransfer.PoToVo(device))\n}\n\n// @Summary 更新唱歌设备\n// @Description 更新唱歌设备\n// @Tags 唱歌设备\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateSingingDeviceReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.SingingDeviceVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/singing-device/update [post]\nfunc (controller *SingingDeviceController) UpdateSingingDevice(ctx *gin.Context) {\n\treqDto := req.UpdateSingingDeviceReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\tdevice, err := singingDeviceService.FindSingingDeviceById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Type != nil {\n\t\tdevice.Type = reqDto.Type\n\t}\n\tif reqDto.IpAddress != nil {\n\t\tdevice.IpAddress = reqDto.IpAddress\n\t}\n\terr = singingDeviceService.UpdateSingingDevice(ctx, device)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, singingDeviceTransfer.PoToVo(*device))\n}\n\n// @Summary 删除唱歌设备\n// @Description 删除唱歌设备\n// @Tags 唱歌设备\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteSingingDeviceReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/singing-device/delete [post]\nfunc (controller *SingingDeviceController) DeleteSingingDevice(ctx *gin.Context) {\n\treqDto := req.DeleteSingingDeviceReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = singingDeviceService.DeleteSingingDevice(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询唱歌设备\n// @Description 查询唱歌设备\n// @Tags 唱歌设备\n// @Accept json\n// @Produce json\n// @Param body body req.QuerySingingDeviceReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.SingingDeviceVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/singing-device/query [post]\nfunc (controller *SingingDeviceController) QuerySingingDevices(ctx *gin.Context) {\n\treqDto := req.QuerySingingDeviceReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := singingDeviceService.FindAllSingingDevice(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.SingingDeviceVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, singingDeviceTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询唱歌设备列表\n// @Description 查询唱歌设备列表\n// @Tags 唱歌设备\n// @Accept json\n// @Produce json\n// @Param body body req.QuerySingingDeviceReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.SingingDeviceVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/singing-device/list [post]\nfunc (a *SingingDeviceController) ListSingingDevices(ctx *gin.Context) {\n\treqDto := req.QuerySingingDeviceReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := singingDeviceService.FindAllSingingDeviceWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.SingingDeviceVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.SingingDeviceVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, singingDeviceTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype SingingDeviceRoute struct {\n}\n\nfunc (s *SingingDeviceRoute) InitSingingDeviceRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tsingingDeviceController := controller.SingingDeviceController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/singing-device/add\", singingDeviceController.AddSingingDevice)    //add\n\t\troute.POST(\"/api/singing-device/update\", singingDeviceController.UpdateSingingDevice) //update\n\t\troute.POST(\"/api/singing-device/delete\", singingDeviceController.DeleteSingingDevice) //delete\n\t\troute.POST(\"/api/singing-device/query\", singingDeviceController.QuerySingingDevices)  //query\n\t\troute.POST(\"/api/singing-device/list\", singingDeviceController.ListSingingDevices)   //list\n\t}\n}"}]