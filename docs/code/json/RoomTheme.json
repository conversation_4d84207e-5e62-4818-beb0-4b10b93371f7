[{"po": "package po\n\n// RoomTheme 房间主题实体\ntype RoomTheme struct {\n\tId          *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`           // ID\n\tVenueId     *string `gorm:\"column:venue_id;type:varchar(64);default:''\" json:\"venueId\"`     // 所属门店ID\n\tName        *string `gorm:\"column:name;type:varchar(255);default:''\" json:\"name\"`           // 房间主题名称\n\tIsDisplayed *bool   `gorm:\"column:is_displayed;type:tinyint;default:0\" json:\"isDisplayed\"`   // 是否显示该主题\n\tDescription *string `gorm:\"column:description;type:text\" json:\"description\"`                  // 主题描述\n\tImageUrl    *string `gorm:\"column:image_url;type:varchar(255);default:''\" json:\"imageUrl\"`   // 主题图片URL\n\tCtime       *int64  `gorm:\"column:ctime;type:bigint;default:0\" json:\"ctime\"`                // 创建时间戳\n\tUtime       *int64  `gorm:\"column:utime;type:bigint;default:0\" json:\"utime\"`                // 更新时间戳\n\tState       *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                   // 状态值\n\tVersion     *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`               // 版本号\n}\n\n// TableName 设置表名\nfunc (RoomTheme) TableName() string {\n\treturn \"room_theme\"\n}\n\nfunc (rt RoomTheme) GetId() string {\n\treturn *rt.Id\n}", "vo": "package vo\n\n// RoomThemeVO 房间主题值对象\ntype RoomThemeVO struct {\n\tId          string `json:\"id\"`          // ID\n\tVenueId     string `json:\"venueId\"`     // 所属门店ID\n\tName        string `json:\"name\"`        // 房间主题名称\n\tIsDisplayed bool   `json:\"isDisplayed\"` // 是否显示该主题\n\tDescription string `json:\"description\"` // 主题描述\n\tImageUrl    string `json:\"imageUrl\"`    // 主题图片URL\n\tCtime       int64  `json:\"ctime\"`       // 创建时间戳\n\tUtime       int64  `json:\"utime\"`       // 更新时间戳\n\tState       int    `json:\"state\"`       // 状态值\n\tVersion     int    `json:\"version\"`     // 版本号\n}", "req_add": "package req\n\n// AddRoomThemeReqDto 创建房间主题请求DTO\ntype AddRoomThemeReqDto struct {\n\tVenueId     *string `json:\"venueId\"`     // 所属门店ID\n\tName        *string `json:\"name\"`        // 房间主题名称\n\tIsDisplayed *bool   `json:\"isDisplayed\"` // 是否显示该主题\n\tDescription *string `json:\"description\"` // 主题描述\n\tImageUrl    *string `json:\"imageUrl\"`    // 主题图片URL\n}", "req_update": "package req\n\n// UpdateRoomThemeReqDto 更新房间主题请求DTO\ntype UpdateRoomThemeReqDto struct {\n\tId          *string `json:\"id\"`          // ID\n\tVenueId     *string `json:\"venueId\"`     // 所属门店ID\n\tName        *string `json:\"name\"`        // 房间主题名称\n\tIsDisplayed *bool   `json:\"isDisplayed\"` // 是否显示该主题\n\tDescription *string `json:\"description\"` // 主题描述\n\tImageUrl    *string `json:\"imageUrl\"`    // 主题图片URL\n}", "req_delete": "package req\n\n// DeleteRoomThemeReqDto 删除房间主题请求DTO\ntype DeleteRoomThemeReqDto struct {\n\tId *string `json:\"id\"` // ID\n}", "req_query": "package req\n\n// QueryRoomThemeReqDto 查询房间主题请求DTO\ntype QueryRoomThemeReqDto struct {\n\tId          *string `json:\"id\"`          // ID\n\tVenueId     *string `json:\"venueId\"`     // 所属门店ID\n\tName        *string `json:\"name\"`        // 房间主题名称\n\tIsDisplayed *bool   `json:\"isDisplayed\"` // 是否显示该主题\n\tDescription *string `json:\"description\"` // 主题描述\n\tImageUrl    *string `json:\"imageUrl\"`    // 主题图片URL\n\tPageNum     *int    `json:\"pageNum\"`     // 页码\n\tPageSize    *int    `json:\"pageSize\"`    // 每页记录数\n}", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype RoomThemeTransfer struct {\n}\n\nfunc (transfer *RoomThemeTransfer) PoToVo(po po.RoomTheme) vo.RoomThemeVO {\n\tvo := vo.RoomThemeVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *RoomThemeTransfer) VoToPo(vo vo.RoomThemeVO) po.RoomTheme {\n\tpo := po.RoomTheme{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype RoomThemeService struct {\n}\n\nfunc (service *RoomThemeService) CreateRoomTheme(logCtx *gin.Context, roomTheme *po.RoomTheme) error {\n\treturn Save(roomTheme)\n}\n\nfunc (service *RoomThemeService) UpdateRoomTheme(logCtx *gin.Context, roomTheme *po.RoomTheme) error {\n\treturn Update(roomTheme)\n}\n\nfunc (service *RoomThemeService) DeleteRoomTheme(logCtx *gin.Context, id string) error {\n\treturn Delete(po.RoomTheme{Id: &id})\n}\n\nfunc (service *RoomThemeService) FindRoomThemeById(logCtx *gin.Context, id string) (roomTheme *po.RoomTheme, err error) {\n\troomTheme = &po.RoomTheme{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(roomTheme).Error\n\treturn\n}\n\nfunc (service *RoomThemeService) FindAllRoomTheme(logCtx *gin.Context, reqDto *req.QueryRoomThemeReqDto) (list *[]po.RoomTheme, err error) {\n\tdb := model.DBSlave.Self.Model(&po.RoomTheme{})\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.VenueId != nil && *reqDto.VenueId != \"\" {\n\t\tdb = db.Where(\"venue_id=?\", *reqDto.VenueId)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name LIKE ?\", \"%\"+*reqDto.Name+\"%\")\n\t}\n\tif reqDto.IsDisplayed != nil {\n\t\tdb = db.Where(\"is_displayed=?\", *reqDto.IsDisplayed)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.RoomTheme{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *RoomThemeService) FindAllRoomThemeWithPagination(logCtx *gin.Context, reqDto *req.QueryRoomThemeReqDto) (list *[]po.RoomTheme, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.RoomTheme{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.VenueId != nil && *reqDto.VenueId != \"\" {\n\t\tdb = db.Where(\"venue_id=?\", *reqDto.VenueId)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name LIKE ?\", \"%\"+*reqDto.Name+\"%\")\n\t}\n\tif reqDto.IsDisplayed != nil {\n\t\tdb = db.Where(\"is_displayed=?\", *reqDto.IsDisplayed)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.RoomTheme{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype RoomThemeController struct{}\n\nvar (\n\troomThemeService  = impl.RoomThemeService{}\n\troomThemeTransfer = transfer.RoomThemeTransfer{}\n)\n\n// @Summary 添加房间主题\n// @Description 添加房间主题\n// @Tags 房间主题\n// @Accept json\n// @Produce json\n// @Param body body req.AddRoomThemeReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.RoomThemeVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/roomTheme/add [post]\nfunc (controller *RoomThemeController) AddRoomTheme(ctx *gin.Context) {\n\treqDto := req.AddRoomThemeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\troomTheme := po.RoomTheme{}\n\tif reqDto.VenueId != nil {\n\t\troomTheme.VenueId = reqDto.VenueId\n\t}\n\tif reqDto.Name != nil {\n\t\troomTheme.Name = reqDto.Name\n\t}\n\tif reqDto.IsDisplayed != nil {\n\t\troomTheme.IsDisplayed = reqDto.IsDisplayed\n\t}\n\tif reqDto.Description != nil {\n\t\troomTheme.Description = reqDto.Description\n\t}\n\tif reqDto.ImageUrl != nil {\n\t\troomTheme.ImageUrl = reqDto.ImageUrl\n\t}\n\n\terr = roomThemeService.CreateRoomTheme(ctx, &roomTheme)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, roomThemeTransfer.PoToVo(roomTheme))\n}\n\n// @Summary 更新房间主题\n// @Description 更新房间主题\n// @Tags 房间主题\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateRoomThemeReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.RoomThemeVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/roomTheme/update [post]\nfunc (controller *RoomThemeController) UpdateRoomTheme(ctx *gin.Context) {\n\treqDto := req.UpdateRoomThemeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\n\troomTheme, err := roomThemeService.FindRoomThemeById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tif reqDto.VenueId != nil {\n\t\troomTheme.VenueId = reqDto.VenueId\n\t}\n\tif reqDto.Name != nil {\n\t\troomTheme.Name = reqDto.Name\n\t}\n\tif reqDto.IsDisplayed != nil {\n\t\troomTheme.IsDisplayed = reqDto.IsDisplayed\n\t}\n\tif reqDto.Description != nil {\n\t\troomTheme.Description = reqDto.Description\n\t}\n\tif reqDto.ImageUrl != nil {\n\t\troomTheme.ImageUrl = reqDto.ImageUrl\n\t}\n\n\terr = roomThemeService.UpdateRoomTheme(ctx, roomTheme)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, roomThemeTransfer.PoToVo(*roomTheme))\n}\n\n// @Summary 删除房间主题\n// @Description 删除房间主题\n// @Tags 房间主题\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteRoomThemeReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/roomTheme/delete [post]\nfunc (controller *RoomThemeController) DeleteRoomTheme(ctx *gin.Context) {\n\treqDto := req.DeleteRoomThemeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\terr = roomThemeService.DeleteRoomTheme(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询房间主题\n// @Description 查询房间主题\n// @Tags 房间主题\n// @Accept json\n// @Produce json\n// @Param body body req.QueryRoomThemeReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.RoomThemeVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/roomTheme/query [post]\nfunc (controller *RoomThemeController) QueryRoomThemes(ctx *gin.Context) {\n\treqDto := req.QueryRoomThemeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tlist, err := roomThemeService.FindAllRoomTheme(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tnewList := []vo.RoomThemeVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, roomThemeTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询房间主题列表\n// @Description 查询房间主题列表\n// @Tags 房间主题\n// @Accept json\n// @Produce json\n// @Param body body req.QueryRoomThemeReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.RoomThemeVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/roomTheme/list [post]\nfunc (controller *RoomThemeController) ListRoomThemes(ctx *gin.Context) {\n\treqDto := req.QueryRoomThemeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\n\tlist, totalCount, err := roomThemeService.FindAllRoomThemeWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tpage := vo.PageVO[[]vo.RoomThemeVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.RoomThemeVO{}\n\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, roomThemeTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype RoomThemeRoute struct {\n}\n\nfunc (s *RoomThemeRoute) InitRoomThemeRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\troomThemeController := controller.RoomThemeController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/roomTheme/add\", roomThemeController.AddRoomTheme)       // add\n\t\troute.POST(\"/api/roomTheme/update\", roomThemeController.UpdateRoomTheme) // update\n\t\troute.POST(\"/api/roomTheme/delete\", roomThemeController.DeleteRoomTheme) // delete\n\t\troute.POST(\"/api/roomTheme/query\", roomThemeController.QueryRoomThemes)  // query\n\t\troute.POST(\"/api/roomTheme/list\", roomThemeController.ListRoomThemes)    // list\n\t}\n}"}]