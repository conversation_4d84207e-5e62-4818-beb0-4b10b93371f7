[{"po": "package po\n\n// RoomType 房间类型实体\ntype RoomType struct {\n\tId                   *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`                    // ID\n\tVenueId              *string `gorm:\"column:venue_id;type:varchar(64);default:''\" json:\"venueId\"`              // 所属门店ID\n\tName                 *string `gorm:\"column:name;type:varchar(255);default:''\" json:\"name\"`                     // 房间类型名称\n\tConsumptionMode      *string `gorm:\"column:consumption_mode;type:varchar(64);default:''\" json:\"consumptionMode\"` // 消费模式\n\tHighConsumptionAlert *int64  `gorm:\"column:high_consumption_alert;type:int;default:0\" json:\"highConsumptionAlert\"` // 高消费警报金额\n\tTimeChargeBasePlan   *string `gorm:\"column:time_charge_base_plan;type:varchar(64);default:''\" json:\"timeChargeBasePlan\"` // 买钟基础价格方案\n\tPhoto                *string `gorm:\"column:photo;type:varchar(255);default:''\" json:\"photo\"`                    // 房间类型照片\n\tRemark              *string `gorm:\"column:remark;type:varchar(255);default:''\" json:\"remark\"`                  // 备注\n\tDistributionChannel *string `gorm:\"column:distribution_channel;type:varchar(64);default:''\" json:\"distributionChannel\"` // 分销渠道\n\tIsDisplayed         *bool   `gorm:\"column:is_displayed;type:tinyint(1);default:0\" json:\"isDisplayed\"`           // 是否显示\n\tCtime               *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                             // 创建时间戳\n\tUtime               *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                             // 更新时间戳\n\tState               *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                             // 状态值\n\tVersion             *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`                         // 版本号\n}\n\n// TableName 设置表名\nfunc (RoomType) TableName() string {\n\treturn \"room_type\"\n}\n\nfunc (r RoomType) GetId() string {\n\treturn *r.Id\n}\n", "vo": "package vo\n\n// RoomTypeVO 房间类型值对象\ntype RoomTypeVO struct {\n\tId                   string `json:\"id\"`                    // ID\n\tVenueId              string `json:\"venueId\"`              // 所属门店ID\n\tName                 string `json:\"name\"`                 // 房间类型名称\n\tConsumptionMode      string `json:\"consumptionMode\"`      // 消费模式\n\tHighConsumptionAlert int64  `json:\"highConsumptionAlert\"` // 高消费警报金额\n\tTimeChargeBasePlan   string `json:\"timeChargeBasePlan\"`   // 买钟基础价格方案\n\tPhoto                string `json:\"photo\"`                // 房间类型照片\n\tRemark              string `json:\"remark\"`              // 备注\n\tDistributionChannel string `json:\"distributionChannel\"` // 分销渠道\n\tIsDisplayed         bool   `json:\"isDisplayed\"`         // 是否显示\n\tCtime               int64  `json:\"ctime\"`               // 创建时间戳\n\tUtime               int64  `json:\"utime\"`               // 更新时间戳\n\tState               int    `json:\"state\"`               // 状态值\n\tVersion             int    `json:\"version\"`             // 版本号\n}\n", "req_add": "package req\n\n// AddRoomTypeReqDto 创建房间类型请求DTO\ntype AddRoomTypeReqDto struct {\n\tVenueId              *string `json:\"venueId\"`              // 所属门店ID\n\tName                 *string `json:\"name\"`                 // 房间类型名称\n\tConsumptionMode      *string `json:\"consumptionMode\"`      // 消费模式\n\tHighConsumptionAlert *int64  `json:\"highConsumptionAlert\"` // 高消费警报金额\n\tTimeChargeBasePlan   *string `json:\"timeChargeBasePlan\"`   // 买钟基础价格方案\n\tPhoto                *string `json:\"photo\"`                // 房间类型照片\n\tRemark              *string `json:\"remark\"`              // 备注\n\tDistributionChannel *string `json:\"distributionChannel\"` // 分销渠道\n\tIsDisplayed         *bool   `json:\"isDisplayed\"`         // 是否显示\n}\n", "req_update": "package req\n\n// UpdateRoomTypeReqDto 更新房间类型请求DTO\ntype UpdateRoomTypeReqDto struct {\n\tId                   *string `json:\"id\"`                   // ID\n\tVenueId              *string `json:\"venueId\"`              // 所属门店ID\n\tName                 *string `json:\"name\"`                 // 房间类型名称\n\tConsumptionMode      *string `json:\"consumptionMode\"`      // 消费模式\n\tHighConsumptionAlert *int64  `json:\"highConsumptionAlert\"` // 高消费警报金额\n\tTimeChargeBasePlan   *string `json:\"timeChargeBasePlan\"`   // 买钟基础价格方案\n\tPhoto                *string `json:\"photo\"`                // 房间类型照片\n\tRemark              *string `json:\"remark\"`              // 备注\n\tDistributionChannel *string `json:\"distributionChannel\"` // 分销渠道\n\tIsDisplayed         *bool   `json:\"isDisplayed\"`         // 是否显示\n}\n", "req_delete": "package req\n\n// DeleteRoomTypeReqDto 删除房间类型请求DTO\ntype DeleteRoomTypeReqDto struct {\n\tId *string `json:\"id\"` // ID\n}\n", "req_query": "package req\n\n// QueryRoomTypeReqDto 查询房间类型请求DTO\ntype QueryRoomTypeReqDto struct {\n\tId                   *string `json:\"id\"`                   // ID\n\tVenueId              *string `json:\"venueId\"`              // 所属门店ID\n\tName                 *string `json:\"name\"`                 // 房间类型名称\n\tConsumptionMode      *string `json:\"consumptionMode\"`      // 消费模式\n\tHighConsumptionAlert *int64  `json:\"highConsumptionAlert\"` // 高消费警报金额\n\tTimeChargeBasePlan   *string `json:\"timeChargeBasePlan\"`   // 买钟基础价格方案\n\tPhoto                *string `json:\"photo\"`                // 房间类型照片\n\tRemark              *string `json:\"remark\"`              // 备注\n\tDistributionChannel *string `json:\"distributionChannel\"` // 分销渠道\n\tIsDisplayed         *bool   `json:\"isDisplayed\"`         // 是否显示\n\tPageNum             *int    `json:\"pageNum\"`             // 页码\n\tPageSize            *int    `json:\"pageSize\"`            // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype RoomTypeTransfer struct {\n}\n\nfunc (transfer *RoomTypeTransfer) PoToVo(po po.RoomType) vo.RoomTypeVO {\n\tvo := vo.RoomTypeVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *RoomTypeTransfer) VoToPo(vo vo.RoomTypeVO) po.RoomType {\n\tpo := po.RoomType{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype RoomTypeService struct {\n}\n\nfunc (service *RoomTypeService) CreateRoomType(logCtx *gin.Context, roomType *po.RoomType) error {\n\treturn Save(roomType)\n}\n\nfunc (service *RoomTypeService) UpdateRoomType(logCtx *gin.Context, roomType *po.RoomType) error {\n\treturn Update(roomType)\n}\n\nfunc (service *RoomTypeService) DeleteRoomType(logCtx *gin.Context, id string) error {\n\treturn Delete(po.RoomType{Id: &id})\n}\n\nfunc (service *RoomTypeService) FindRoomTypeById(logCtx *gin.Context, id string) (roomType *po.RoomType, err error) {\n\troomType = &po.RoomType{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(roomType).Error\n\treturn\n}\n\nfunc (service *RoomTypeService) FindAllRoomType(logCtx *gin.Context, reqDto *req.QueryRoomTypeReqDto) (list *[]po.RoomType, err error) {\n\tdb := model.DBSlave.Self.Model(&po.RoomType{})\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.VenueId != nil && *reqDto.VenueId != \"\" {\n\t\tdb = db.Where(\"venue_id=?\", *reqDto.VenueId)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name LIKE ?\", \"%\"+*reqDto.Name+\"%\")\n\t}\n\tif reqDto.ConsumptionMode != nil && *reqDto.ConsumptionMode != \"\" {\n\t\tdb = db.Where(\"consumption_mode=?\", *reqDto.ConsumptionMode)\n\t}\n\tif reqDto.HighConsumptionAlert != nil {\n\t\tdb = db.Where(\"high_consumption_alert=?\", *reqDto.HighConsumptionAlert)\n\t}\n\tif reqDto.TimeChargeBasePlan != nil && *reqDto.TimeChargeBasePlan != \"\" {\n\t\tdb = db.Where(\"time_charge_base_plan=?\", *reqDto.TimeChargeBasePlan)\n\t}\n\tif reqDto.Photo != nil && *reqDto.Photo != \"\" {\n\t\tdb = db.Where(\"photo=?\", *reqDto.Photo)\n\t}\n\tif reqDto.Remark != nil && *reqDto.Remark != \"\" {\n\t\tdb = db.Where(\"remark LIKE ?\", \"%\"+*reqDto.Remark+\"%\")\n\t}\n\tif reqDto.DistributionChannel != nil && *reqDto.DistributionChannel != \"\" {\n\t\tdb = db.Where(\"distribution_channel=?\", *reqDto.DistributionChannel)\n\t}\n\tif reqDto.IsDisplayed != nil {\n\t\tdb = db.Where(\"is_displayed=?\", *reqDto.IsDisplayed)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.RoomType{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *RoomTypeService) FindAllRoomTypeWithPagination(logCtx *gin.Context, reqDto *req.QueryRoomTypeReqDto) (list *[]po.RoomType, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.RoomType{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.VenueId != nil && *reqDto.VenueId != \"\" {\n\t\tdb = db.Where(\"venue_id=?\", *reqDto.VenueId)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name LIKE ?\", \"%\"+*reqDto.Name+\"%\")\n\t}\n\tif reqDto.ConsumptionMode != nil && *reqDto.ConsumptionMode != \"\" {\n\t\tdb = db.Where(\"consumption_mode=?\", *reqDto.ConsumptionMode)\n\t}\n\tif reqDto.HighConsumptionAlert != nil {\n\t\tdb = db.Where(\"high_consumption_alert=?\", *reqDto.HighConsumptionAlert)\n\t}\n\tif reqDto.TimeChargeBasePlan != nil && *reqDto.TimeChargeBasePlan != \"\" {\n\t\tdb = db.Where(\"time_charge_base_plan=?\", *reqDto.TimeChargeBasePlan)\n\t}\n\tif reqDto.Photo != nil && *reqDto.Photo != \"\" {\n\t\tdb = db.Where(\"photo=?\", *reqDto.Photo)\n\t}\n\tif reqDto.Remark != nil && *reqDto.Remark != \"\" {\n\t\tdb = db.Where(\"remark LIKE ?\", \"%\"+*reqDto.Remark+\"%\")\n\t}\n\tif reqDto.DistributionChannel != nil && *reqDto.DistributionChannel != \"\" {\n\t\tdb = db.Where(\"distribution_channel=?\", *reqDto.DistributionChannel)\n\t}\n\tif reqDto.IsDisplayed != nil {\n\t\tdb = db.Where(\"is_displayed=?\", *reqDto.IsDisplayed)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.RoomType{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype RoomTypeController struct{}\n\nvar (\n\troomTypeService  = impl.RoomTypeService{}\n\troomTypeTransfer = transfer.RoomTypeTransfer{}\n)\n\n// @Summary 添加房间类型\n// @Description 添加房间类型\n// @Tags 房间类型\n// @Accept json\n// @Produce json\n// @Param body body req.AddRoomTypeReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.RoomTypeVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/roomType/add [post]\nfunc (controller *RoomTypeController) AddRoomType(ctx *gin.Context) {\n\treqDto := req.AddRoomTypeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\troomType := po.RoomType{}\n\tif reqDto.VenueId != nil {\n\t\troomType.VenueId = reqDto.VenueId\n\t}\n\tif reqDto.Name != nil {\n\t\troomType.Name = reqDto.Name\n\t}\n\tif reqDto.ConsumptionMode != nil {\n\t\troomType.ConsumptionMode = reqDto.ConsumptionMode\n\t}\n\tif reqDto.HighConsumptionAlert != nil {\n\t\troomType.HighConsumptionAlert = reqDto.HighConsumptionAlert\n\t}\n\tif reqDto.TimeChargeBasePlan != nil {\n\t\troomType.TimeChargeBasePlan = reqDto.TimeChargeBasePlan\n\t}\n\tif reqDto.Photo != nil {\n\t\troomType.Photo = reqDto.Photo\n\t}\n\tif reqDto.Remark != nil {\n\t\troomType.Remark = reqDto.Remark\n\t}\n\tif reqDto.DistributionChannel != nil {\n\t\troomType.DistributionChannel = reqDto.DistributionChannel\n\t}\n\tif reqDto.IsDisplayed != nil {\n\t\troomType.IsDisplayed = reqDto.IsDisplayed\n\t}\n\n\terr = roomTypeService.CreateRoomType(ctx, &roomType)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, roomTypeTransfer.PoToVo(roomType))\n}\n\n// @Summary 更新房间类型\n// @Description 更新房间类型\n// @Tags 房间类型\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateRoomTypeReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.RoomTypeVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/roomType/update [post]\nfunc (controller *RoomTypeController) UpdateRoomType(ctx *gin.Context) {\n\treqDto := req.UpdateRoomTypeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\n\troomType, err := roomTypeService.FindRoomTypeById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tif reqDto.VenueId != nil {\n\t\troomType.VenueId = reqDto.VenueId\n\t}\n\tif reqDto.Name != nil {\n\t\troomType.Name = reqDto.Name\n\t}\n\tif reqDto.ConsumptionMode != nil {\n\t\troomType.ConsumptionMode = reqDto.ConsumptionMode\n\t}\n\tif reqDto.HighConsumptionAlert != nil {\n\t\troomType.HighConsumptionAlert = reqDto.HighConsumptionAlert\n\t}\n\tif reqDto.TimeChargeBasePlan != nil {\n\t\troomType.TimeChargeBasePlan = reqDto.TimeChargeBasePlan\n\t}\n\tif reqDto.Photo != nil {\n\t\troomType.Photo = reqDto.Photo\n\t}\n\tif reqDto.Remark != nil {\n\t\troomType.Remark = reqDto.Remark\n\t}\n\tif reqDto.DistributionChannel != nil {\n\t\troomType.DistributionChannel = reqDto.DistributionChannel\n\t}\n\tif reqDto.IsDisplayed != nil {\n\t\troomType.IsDisplayed = reqDto.IsDisplayed\n\t}\n\n\terr = roomTypeService.UpdateRoomType(ctx, roomType)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, roomTypeTransfer.PoToVo(*roomType))\n}\n\n// @Summary 删除房间类型\n// @Description 删除房间类型\n// @Tags 房间类型\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteRoomTypeReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/roomType/delete [post]\nfunc (controller *RoomTypeController) DeleteRoomType(ctx *gin.Context) {\n\treqDto := req.DeleteRoomTypeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\terr = roomTypeService.DeleteRoomType(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询房间类型\n// @Description 查询房间类型\n// @Tags 房间类型\n// @Accept json\n// @Produce json\n// @Param body body req.QueryRoomTypeReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.RoomTypeVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/roomType/query [post]\nfunc (controller *RoomTypeController) QueryRoomTypes(ctx *gin.Context) {\n\treqDto := req.QueryRoomTypeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tlist, err := roomTypeService.FindAllRoomType(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tnewList := []vo.RoomTypeVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, roomTypeTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询房间类型列表\n// @Description 查询房间类型列表\n// @Tags 房间类型\n// @Accept json\n// @Produce json\n// @Param body body req.QueryRoomTypeReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.RoomTypeVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/roomType/list [post]\nfunc (controller *RoomTypeController) ListRoomTypes(ctx *gin.Context) {\n\treqDto := req.QueryRoomTypeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\n\tlist, totalCount, err := roomTypeService.FindAllRoomTypeWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tpage := vo.PageVO[[]vo.RoomTypeVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.RoomTypeVO{}\n\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, roomTypeTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype RoomTypeRoute struct {\n}\n\nfunc (s *RoomTypeRoute) InitRoomTypeRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\troomTypeController := controller.RoomTypeController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/roomType/add\", roomTypeController.AddRoomType)       // add\n\t\troute.POST(\"/api/roomType/update\", roomTypeController.UpdateRoomType) // update\n\t\troute.POST(\"/api/roomType/delete\", roomTypeController.DeleteRoomType) // delete\n\t\troute.POST(\"/api/roomType/query\", roomTypeController.QueryRoomTypes)  // query\n\t\troute.POST(\"/api/roomType/list\", roomTypeController.ListRoomTypes)    // list\n\t}\n}\n"}]