[{"po": "package po\n\n// Reward 奖励实体\ntype Reward struct {\n\tId         *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`          // ID\n\tRewardId   *string `gorm:\"column:reward_id;type:varchar(64);default:''\" json:\"rewardId\"`    // 奖励ID\n\tAmount     *int64  `gorm:\"column:amount;type:bigint;default:0\" json:\"amount\"`              // 金额\n\tRewardTime *int64  `gorm:\"column:reward_time;type:bigint;default:0\" json:\"rewardTime\"`      // 奖励时间\n\tCtime      *int64  `gorm:\"column:ctime;type:bigint;default:0\" json:\"ctime\"`                // 创建时间戳\n\tUtime      *int64  `gorm:\"column:utime;type:bigint;default:0\" json:\"utime\"`                // 更新时间戳\n\tState      *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                   // 状态值\n\tVersion    *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`               // 版本号\n}\n\n// TableName 设置表名\nfunc (Reward) TableName() string {\n\treturn \"reward\"\n}\n\nfunc (r Reward) GetId() string {\n\treturn *r.Id\n}", "vo": "package vo\n\n// RewardVO 奖励信息值对象\ntype RewardVO struct {\n\tId         string `json:\"id\"`          // ID\n\tRewardId   string `json:\"rewardId\"`    // 奖励ID\n\tAmount     int64  `json:\"amount\"`      // 金额\n\tRewardTime int64  `json:\"rewardTime\"`  // 奖励时间\n\tCtime      int64  `json:\"ctime\"`       // 创建时间戳\n\tUtime      int64  `json:\"utime\"`       // 更新时间戳\n\tState      int    `json:\"state\"`       // 状态值\n\tVersion    int    `json:\"version\"`     // 版本号\n}", "req_add": "package req\n\n// AddRewardReqDto 创建奖励请求DTO\ntype AddRewardReqDto struct {\n\tRewardId   *string `json:\"rewardId\"`   // 奖励ID\n\tAmount     *int64  `json:\"amount\"`     // 金额\n\tRewardTime *int64  `json:\"rewardTime\"` // 奖励时间\n}", "req_update": "package req\n\ntype UpdateRewardReqDto struct {\n\tId         *string `json:\"id\"`         // ID\n\tRewardId   *string `json:\"rewardId\"`   // 奖励ID\n\tAmount     *int64  `json:\"amount\"`     // 金额\n\tRewardTime *int64  `json:\"rewardTime\"` // 奖励时间\n}", "req_delete": "package req\n\ntype DeleteRewardReqDto struct {\n\tId *string `json:\"id\"` // ID\n}", "req_query": "package req\n\ntype QueryRewardReqDto struct {\n\tId         *string `json:\"id\"`         // ID\n\tRewardId   *string `json:\"rewardId\"`   // 奖励ID\n\tAmount     *int64  `json:\"amount\"`     // 金额\n\tRewardTime *int64  `json:\"rewardTime\"` // 奖励时间\n\tPageNum    *int    `json:\"pageNum\"`    // 页码\n\tPageSize   *int    `json:\"pageSize\"`   // 每页记录数\n}", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype RewardTransfer struct {\n}\n\nfunc (transfer *RewardTransfer) PoToVo(po po.Reward) vo.RewardVO {\n\tvo := vo.RewardVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *RewardTransfer) VoToPo(vo vo.RewardVO) po.Reward {\n\tpo := po.Reward{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype RewardService struct {\n}\n\nfunc (service *RewardService) CreateReward(logCtx *gin.Context, reward *po.Reward) error {\n\treturn Save(reward)\n}\n\nfunc (service *RewardService) UpdateReward(logCtx *gin.Context, reward *po.Reward) error {\n\treturn Update(reward)\n}\n\nfunc (service *RewardService) DeleteReward(logCtx *gin.Context, id string) error {\n\treturn Delete(po.Reward{Id: &id})\n}\n\nfunc (service *RewardService) FindRewardById(logCtx *gin.Context, id string) (reward *po.Reward, err error) {\n\treward = &po.Reward{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(reward).Error\n\treturn\n}\n\nfunc (service *RewardService) FindAllReward(logCtx *gin.Context, reqDto *req.QueryRewardReqDto) (list *[]po.Reward, err error) {\n\tdb := model.DBSlave.Self.Model(&po.Reward{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.Reward{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *RewardService) FindAllRewardWithPagination(logCtx *gin.Context, reqDto *req.QueryRewardReqDto) (list *[]po.Reward, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.Reward{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.Reward{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype RewardController struct{}\n\nvar (\n\trewardService  = impl.RewardService{}\n\trewardTransfer = transfer.RewardTransfer{}\n)\n\n// @Summary 添加奖励\n// @Description 添加奖励\n// @Tags 奖励\n// @Accept json\n// @Produce json\n// @Param body body req.AddRewardReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.RewardVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/reward/add [post]\nfunc (controller *RewardController) AddReward(ctx *gin.Context) {\n\treqDto := req.AddRewardReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\treward := po.Reward{}\n\tif reqDto.RewardId != nil {\n\t\treward.RewardId = reqDto.RewardId\n\t}\n\tif reqDto.Amount != nil {\n\t\treward.Amount = reqDto.Amount\n\t}\n\tif reqDto.RewardTime != nil {\n\t\treward.RewardTime = reqDto.RewardTime\n\t}\n\terr = rewardService.CreateReward(ctx, &reward)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, rewardTransfer.PoToVo(reward))\n}\n\n// @Summary 更新奖励\n// @Description 更新奖励\n// @Tags 奖励\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateRewardReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.RewardVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/reward/update [post]\nfunc (controller *RewardController) UpdateReward(ctx *gin.Context) {\n\treqDto := req.UpdateRewardReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\treward, err := rewardService.FindRewardById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.RewardId != nil {\n\t\treward.RewardId = reqDto.RewardId\n\t}\n\tif reqDto.Amount != nil {\n\t\treward.Amount = reqDto.Amount\n\t}\n\tif reqDto.RewardTime != nil {\n\t\treward.RewardTime = reqDto.RewardTime\n\t}\n\terr = rewardService.UpdateReward(ctx, reward)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, rewardTransfer.PoToVo(*reward))\n}\n\n// @Summary 删除奖励\n// @Description 删除奖励\n// @Tags 奖励\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteRewardReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/reward/delete [post]\nfunc (controller *RewardController) DeleteReward(ctx *gin.Context) {\n\treqDto := req.DeleteRewardReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = rewardService.DeleteReward(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询奖励\n// @Description 查询奖励\n// @Tags 奖励\n// @Accept json\n// @Produce json\n// @Param body body req.QueryRewardReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.RewardVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/reward/query [post]\nfunc (controller *RewardController) QueryRewards(ctx *gin.Context) {\n\treqDto := req.QueryRewardReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := rewardService.FindAllReward(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.RewardVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, rewardTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询奖励列表\n// @Description 查询奖励列表\n// @Tags 奖励\n// @Accept json\n// @Produce json\n// @Param body body req.QueryRewardReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.RewardVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/reward/list [post]\nfunc (a *RewardController) ListRewards(ctx *gin.Context) {\n\treqDto := req.QueryRewardReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := rewardService.FindAllRewardWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.RewardVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.RewardVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, rewardTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype RewardRoute struct {\n}\n\nfunc (s *RewardRoute) InitRewardRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\trewardController := controller.RewardController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/reward/add\", rewardController.AddReward)       //add\n\t\troute.POST(\"/api/reward/update\", rewardController.UpdateReward) //update\n\t\troute.POST(\"/api/reward/delete\", rewardController.DeleteReward) //delete\n\t\troute.POST(\"/api/reward/query\", rewardController.QueryRewards)  //query\n\t\troute.POST(\"/api/reward/list\", rewardController.ListRewards)    //list\n\t}\n}"}]