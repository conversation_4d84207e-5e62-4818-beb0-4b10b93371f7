[{"po": "package po\n\n// VodSettings VOD服务配置实体\ntype VodSettings struct {\n\tId                *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`                // 唯一ID\n\tVodServerIP       *string `gorm:\"column:vod_server_ip;type:varchar(64);default:''\" json:\"vodServerIP\"`    // VOD服务器IP地址\n\tErpServerIP       *string `gorm:\"column:erp_server_ip;type:varchar(64);default:''\" json:\"erpServerIP\"`    // ERP服务器IP地址\n\tSongScreenReminder *bool   `gorm:\"column:song_screen_reminder;type:bool;default:false\" json:\"songScreenReminder\"` // 点歌屏提醒\n\tSequencerModel    *string `gorm:\"column:sequencer_model;type:varchar(64);default:''\" json:\"sequencerModel\"` // 控制器型号\n\tCtime            *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                    // 创建时间戳\n\tUtime            *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                    // 更新时间戳\n\tState            *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                    // 状态值\n\tVersion          *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`                // 版本号\n}\n\n// TableName 设置表名\nfunc (VodSettings) TableName() string {\n\treturn \"vod_settings\"\n}\n\nfunc (v VodSettings) GetId() string {\n\treturn *v.Id\n}\n", "vo": "package vo\n\n// VodSettingsVO VOD服务配置值对象\ntype VodSettingsVO struct {\n\tId                string `json:\"id\"`                // 唯一ID\n\tVodServerIP       string `json:\"vodServerIP\"`        // VOD服务器IP地址\n\tErpServerIP       string `json:\"erpServerIP\"`        // ERP服务器IP地址\n\tSongScreenReminder bool   `json:\"songScreenReminder\"` // 点歌屏提醒\n\tSequencerModel    string `json:\"sequencerModel\"`     // 控制器型号\n\tCtime             int64  `json:\"ctime\"`             // 创建时间戳\n\tUtime             int64  `json:\"utime\"`             // 更新时间戳\n\tState             int    `json:\"state\"`             // 状态值\n\tVersion           int    `json:\"version\"`           // 版本号\n}\n", "req_add": "package req\n\n// AddVodSettingsReqDto 创建VOD设置请求DTO\ntype AddVodSettingsReqDto struct {\n\tVodServerIP       *string `json:\"vodServerIP\"`        // VOD服务器IP地址\n\tErpServerIP       *string `json:\"erpServerIP\"`        // ERP服务器IP地址\n\tSongScreenReminder *bool   `json:\"songScreenReminder\"` // 点歌屏提醒\n\tSequencerModel    *string `json:\"sequencerModel\"`     // 控制器型号\n}\n", "req_update": "package req\n\n// UpdateVodSettingsReqDto 更新VOD设置请求DTO\ntype UpdateVodSettingsReqDto struct {\n\tId                *string `json:\"id\"`                // 唯一ID\n\tVodServerIP       *string `json:\"vodServerIP\"`        // VOD服务器IP地址\n\tErpServerIP       *string `json:\"erpServerIP\"`        // ERP服务器IP地址\n\tSongScreenReminder *bool   `json:\"songScreenReminder\"` // 点歌屏提醒\n\tSequencerModel    *string `json:\"sequencerModel\"`     // 控制器型号\n}\n", "req_delete": "package req\n\n// DeleteVodSettingsReqDto 删除VOD设置请求DTO\ntype DeleteVodSettingsReqDto struct {\n\tId *string `json:\"id\"` // 唯一ID\n}\n", "req_query": "package req\n\n// QueryVodSettingsReqDto 查询VOD设置请求DTO\ntype QueryVodSettingsReqDto struct {\n\tId                *string `json:\"id\"`                // 唯一ID\n\tVodServerIP       *string `json:\"vodServerIP\"`        // VOD服务器IP地址\n\tErpServerIP       *string `json:\"erpServerIP\"`        // ERP服务器IP地址\n\tSongScreenReminder *bool   `json:\"songScreenReminder\"` // 点歌屏提醒\n\tSequencerModel    *string `json:\"sequencerModel\"`     // 控制器型号\n\tPageNum           *int    `json:\"pageNum\"`           // 页码\n\tPageSize          *int    `json:\"pageSize\"`          // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype VodSettingsTransfer struct {\n}\n\nfunc (transfer *VodSettingsTransfer) PoToVo(po po.VodSettings) vo.VodSettingsVO {\n\tvo := vo.VodSettingsVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *VodSettingsTransfer) VoToPo(vo vo.VodSettingsVO) po.VodSettings {\n\tpo := po.VodSettings{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype VodSettingsService struct {\n}\n\nfunc (service *VodSettingsService) CreateVodSettings(logCtx *gin.Context, vodSettings *po.VodSettings) error {\n\treturn Save(vodSettings)\n}\n\nfunc (service *VodSettingsService) UpdateVodSettings(logCtx *gin.Context, vodSettings *po.VodSettings) error {\n\treturn Update(vodSettings)\n}\n\nfunc (service *VodSettingsService) DeleteVodSettings(logCtx *gin.Context, id string) error {\n\treturn Delete(po.VodSettings{Id: &id})\n}\n\nfunc (service *VodSettingsService) FindVodSettingsById(logCtx *gin.Context, id string) (vodSettings *po.VodSettings, err error) {\n\tvodSettings = &po.VodSettings{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(vodSettings).Error\n\treturn\n}\n\nfunc (service *VodSettingsService) FindAllVodSettings(logCtx *gin.Context, reqDto *req.QueryVodSettingsReqDto) (list *[]po.VodSettings, err error) {\n\tdb := model.DBSlave.Self.Model(&po.VodSettings{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.VodServerIP != nil && *reqDto.VodServerIP != \"\" {\n\t\tdb = db.Where(\"vod_server_ip=?\", *reqDto.VodServerIP)\n\t}\n\tif reqDto.ErpServerIP != nil && *reqDto.ErpServerIP != \"\" {\n\t\tdb = db.Where(\"erp_server_ip=?\", *reqDto.ErpServerIP)\n\t}\n\tif reqDto.SongScreenReminder != nil {\n\t\tdb = db.Where(\"song_screen_reminder=?\", *reqDto.SongScreenReminder)\n\t}\n\tif reqDto.SequencerModel != nil && *reqDto.SequencerModel != \"\" {\n\t\tdb = db.Where(\"sequencer_model=?\", *reqDto.SequencerModel)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.VodSettings{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *VodSettingsService) FindAllVodSettingsWithPagination(logCtx *gin.Context, reqDto *req.QueryVodSettingsReqDto) (list *[]po.VodSettings, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.VodSettings{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.VodServerIP != nil && *reqDto.VodServerIP != \"\" {\n\t\tdb = db.Where(\"vod_server_ip=?\", *reqDto.VodServerIP)\n\t}\n\tif reqDto.ErpServerIP != nil && *reqDto.ErpServerIP != \"\" {\n\t\tdb = db.Where(\"erp_server_ip=?\", *reqDto.ErpServerIP)\n\t}\n\tif reqDto.SongScreenReminder != nil {\n\t\tdb = db.Where(\"song_screen_reminder=?\", *reqDto.SongScreenReminder)\n\t}\n\tif reqDto.SequencerModel != nil && *reqDto.SequencerModel != \"\" {\n\t\tdb = db.Where(\"sequencer_model=?\", *reqDto.SequencerModel)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.VodSettings{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype VodSettingsController struct{}\n\nvar (\n\tvodSettingsService  = impl.VodSettingsService{}\n\tvodSettingsTransfer = transfer.VodSettingsTransfer{}\n)\n\n// @Summary 添加VOD设置\n// @Description 添加VOD设置\n// @Tags VOD设置\n// @Accept json\n// @Produce json\n// @Param body body req.AddVodSettingsReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.VodSettingsVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/vodSettings/add [post]\nfunc (controller *VodSettingsController) AddVodSettings(ctx *gin.Context) {\n\treqDto := req.AddVodSettingsReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tvodSettings := po.VodSettings{}\n\tif reqDto.VodServerIP != nil {\n\t\tvodSettings.VodServerIP = reqDto.VodServerIP\n\t}\n\tif reqDto.ErpServerIP != nil {\n\t\tvodSettings.ErpServerIP = reqDto.ErpServerIP\n\t}\n\tif reqDto.SongScreenReminder != nil {\n\t\tvodSettings.SongScreenReminder = reqDto.SongScreenReminder\n\t}\n\tif reqDto.SequencerModel != nil {\n\t\tvodSettings.SequencerModel = reqDto.SequencerModel\n\t}\n\n\terr = vodSettingsService.CreateVodSettings(ctx, &vodSettings)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, vodSettingsTransfer.PoToVo(vodSettings))\n}\n\n// @Summary 更新VOD设置\n// @Description 更新VOD设置\n// @Tags VOD设置\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateVodSettingsReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.VodSettingsVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/vodSettings/update [post]\nfunc (controller *VodSettingsController) UpdateVodSettings(ctx *gin.Context) {\n\treqDto := req.UpdateVodSettingsReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\n\tvodSettings, err := vodSettingsService.FindVodSettingsById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tif reqDto.VodServerIP != nil {\n\t\tvodSettings.VodServerIP = reqDto.VodServerIP\n\t}\n\tif reqDto.ErpServerIP != nil {\n\t\tvodSettings.ErpServerIP = reqDto.ErpServerIP\n\t}\n\tif reqDto.SongScreenReminder != nil {\n\t\tvodSettings.SongScreenReminder = reqDto.SongScreenReminder\n\t}\n\tif reqDto.SequencerModel != nil {\n\t\tvodSettings.SequencerModel = reqDto.SequencerModel\n\t}\n\n\terr = vodSettingsService.UpdateVodSettings(ctx, vodSettings)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, vodSettingsTransfer.PoToVo(*vodSettings))\n}\n\n// @Summary 删除VOD设置\n// @Description 删除VOD设置\n// @Tags VOD设置\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteVodSettingsReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/vodSettings/delete [post]\nfunc (controller *VodSettingsController) DeleteVodSettings(ctx *gin.Context) {\n\treqDto := req.DeleteVodSettingsReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\terr = vodSettingsService.DeleteVodSettings(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询VOD设置\n// @Description 查询VOD设置\n// @Tags VOD设置\n// @Accept json\n// @Produce json\n// @Param body body req.QueryVodSettingsReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.VodSettingsVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/vodSettings/query [post]\nfunc (controller *VodSettingsController) QueryVodSettings(ctx *gin.Context) {\n\treqDto := req.QueryVodSettingsReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tlist, err := vodSettingsService.FindAllVodSettings(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tnewList := []vo.VodSettingsVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, vodSettingsTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询VOD设置列表\n// @Description 查询VOD设置列表\n// @Tags VOD设置\n// @Accept json\n// @Produce json\n// @Param body body req.QueryVodSettingsReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.VodSettingsVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/vodSettings/list [post]\nfunc (controller *VodSettingsController) ListVodSettings(ctx *gin.Context) {\n\treqDto := req.QueryVodSettingsReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\n\tlist, totalCount, err := vodSettingsService.FindAllVodSettingsWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tpage := vo.PageVO[[]vo.VodSettingsVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.VodSettingsVO{}\n\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, vodSettingsTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype VodSettingsRoute struct {\n}\n\nfunc (s *VodSettingsRoute) InitVodSettingsRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tvodSettingsController := controller.VodSettingsController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/vodSettings/add\", vodSettingsController.AddVodSettings)       // add\n\t\troute.POST(\"/api/vodSettings/update\", vodSettingsController.UpdateVodSettings)   // update\n\t\troute.POST(\"/api/vodSettings/delete\", vodSettingsController.DeleteVodSettings)   // delete\n\t\troute.POST(\"/api/vodSettings/query\", vodSettingsController.QueryVodSettings)     // query\n\t\troute.POST(\"/api/vodSettings/list\", vodSettingsController.ListVodSettings)       // list\n\t}\n}\n"}]