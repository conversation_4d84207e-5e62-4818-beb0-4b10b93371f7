[{"po": "package po\n\n// TvScreenActivity 电视屏幕活动实体\ntype TvScreenActivity struct {\n\tId           *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`           // ID\n\tActivityType *string `gorm:\"column:activity_type;type:varchar(64);default:''\" json:\"activityType\"` // 活动类型\n\tStartTime    *int64  `gorm:\"column:start_time;type:int;default:0\" json:\"startTime\"`               // 开始时间\n\tEndTime      *int64  `gorm:\"column:end_time;type:int;default:0\" json:\"endTime\"`                   // 结束时间\n\tProductInfo  *string `gorm:\"column:product_info;type:varchar(255);default:''\" json:\"productInfo\"`   // 产品信息\n\tCtime        *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                         // 创建时间\n\tUtime        *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                         // 更新时间\n\tState        *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                         // 状态\n\tVersion      *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`                     // 版本号\n}\n\n// TableName 设置表名\nfunc (TvScreenActivity) TableName() string {\n\treturn \"tv_screen_activity\"\n}\n\nfunc (t TvScreenActivity) GetId() string {\n\treturn *t.Id\n}\n", "vo": "package vo\n\n// TvScreenActivityVO 电视屏幕活动值对象\ntype TvScreenActivityVO struct {\n\tId           string `json:\"id\"`           // ID\n\tActivityType string `json:\"activityType\"` // 活动类型\n\tStartTime    int64  `json:\"startTime\"`    // 开始时间\n\tEndTime      int64  `json:\"endTime\"`      // 结束时间\n\tProductInfo  string `json:\"productInfo\"`  // 产品信息\n\tCtime        int64  `json:\"ctime\"`        // 创建时间\n\tUtime        int64  `json:\"utime\"`        // 更新时间\n\tState        int    `json:\"state\"`        // 状态\n\tVersion      int    `json:\"version\"`      // 版本号\n}\n", "req_add": "package req\n\n// AddTvScreenActivityReqDto 创建电视屏幕活动请求DTO\ntype AddTvScreenActivityReqDto struct {\n\tActivityType *string `json:\"activityType\"` // 活动类型\n\tStartTime    *int64  `json:\"startTime\"`    // 开始时间\n\tEndTime      *int64  `json:\"endTime\"`      // 结束时间\n\tProductInfo  *string `json:\"productInfo\"`  // 产品信息\n}\n", "req_update": "package req\n\n// UpdateTvScreenActivityReqDto 更新电视屏幕活动请求DTO\ntype UpdateTvScreenActivityReqDto struct {\n\tId           *string `json:\"id\"`           // ID\n\tActivityType *string `json:\"activityType\"` // 活动类型\n\tStartTime    *int64  `json:\"startTime\"`    // 开始时间\n\tEndTime      *int64  `json:\"endTime\"`      // 结束时间\n\tProductInfo  *string `json:\"productInfo\"`  // 产品信息\n}\n", "req_delete": "package req\n\n// DeleteTvScreenActivityReqDto 删除电视屏幕活动请求DTO\ntype DeleteTvScreenActivityReqDto struct {\n\tId *string `json:\"id\"` // ID\n}\n", "req_query": "package req\n\n// QueryTvScreenActivityReqDto 查询电视屏幕活动请求DTO\ntype QueryTvScreenActivityReqDto struct {\n\tId           *string `json:\"id\"`           // ID\n\tActivityType *string `json:\"activityType\"` // 活动类型\n\tStartTime    *int64  `json:\"startTime\"`    // 开始时间\n\tEndTime      *int64  `json:\"endTime\"`      // 结束时间\n\tProductInfo  *string `json:\"productInfo\"`  // 产品信息\n\tPageNum      *int    `json:\"pageNum\"`      // 页码\n\tPageSize     *int    `json:\"pageSize\"`     // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype TvScreenActivityTransfer struct {\n}\n\nfunc (transfer *TvScreenActivityTransfer) PoToVo(po po.TvScreenActivity) vo.TvScreenActivityVO {\n\tvo := vo.TvScreenActivityVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *TvScreenActivityTransfer) VoToPo(vo vo.TvScreenActivityVO) po.TvScreenActivity {\n\tpo := po.TvScreenActivity{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype TvScreenActivityService struct {\n}\n\nfunc (service *TvScreenActivityService) CreateTvScreenActivity(logCtx *gin.Context, activity *po.TvScreenActivity) error {\n\treturn Save(activity)\n}\n\nfunc (service *TvScreenActivityService) UpdateTvScreenActivity(logCtx *gin.Context, activity *po.TvScreenActivity) error {\n\treturn Update(activity)\n}\n\nfunc (service *TvScreenActivityService) DeleteTvScreenActivity(logCtx *gin.Context, id string) error {\n\treturn Delete(po.TvScreenActivity{Id: &id})\n}\n\nfunc (service *TvScreenActivityService) FindTvScreenActivityById(logCtx *gin.Context, id string) (activity *po.TvScreenActivity, err error) {\n\tactivity = &po.TvScreenActivity{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(activity).Error\n\treturn\n}\n\nfunc (service *TvScreenActivityService) FindAllTvScreenActivity(logCtx *gin.Context, reqDto *req.QueryTvScreenActivityReqDto) (list *[]po.TvScreenActivity, err error) {\n\tdb := model.DBSlave.Self.Model(&po.TvScreenActivity{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.ActivityType != nil && *reqDto.ActivityType != \"\" {\n\t\tdb = db.Where(\"activity_type=?\", *reqDto.ActivityType)\n\t}\n\tif reqDto.StartTime != nil {\n\t\tdb = db.Where(\"start_time=?\", *reqDto.StartTime)\n\t}\n\tif reqDto.EndTime != nil {\n\t\tdb = db.Where(\"end_time=?\", *reqDto.EndTime)\n\t}\n\tif reqDto.ProductInfo != nil && *reqDto.ProductInfo != \"\" {\n\t\tdb = db.Where(\"product_info=?\", *reqDto.ProductInfo)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.TvScreenActivity{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *TvScreenActivityService) FindAllTvScreenActivityWithPagination(logCtx *gin.Context, reqDto *req.QueryTvScreenActivityReqDto) (list *[]po.TvScreenActivity, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.TvScreenActivity{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.ActivityType != nil && *reqDto.ActivityType != \"\" {\n\t\tdb = db.Where(\"activity_type=?\", *reqDto.ActivityType)\n\t}\n\tif reqDto.StartTime != nil {\n\t\tdb = db.Where(\"start_time=?\", *reqDto.StartTime)\n\t}\n\tif reqDto.EndTime != nil {\n\t\tdb = db.Where(\"end_time=?\", *reqDto.EndTime)\n\t}\n\tif reqDto.ProductInfo != nil && *reqDto.ProductInfo != \"\" {\n\t\tdb = db.Where(\"product_info=?\", *reqDto.ProductInfo)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.TvScreenActivity{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype TvScreenActivityController struct{}\n\nvar (\n\ttvScreenActivityService  = impl.TvScreenActivityService{}\n\ttvScreenActivityTransfer = transfer.TvScreenActivityTransfer{}\n)\n\n// @Summary 添加电视屏幕活动\n// @Description 添加电视屏幕活动\n// @Tags 电视屏幕活动\n// @Accept json\n// @Produce json\n// @Param body body req.AddTvScreenActivityReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.TvScreenActivityVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/tv-screen-activity/add [post]\nfunc (controller *TvScreenActivityController) AddTvScreenActivity(ctx *gin.Context) {\n\treqDto := req.AddTvScreenActivityReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tactivity := po.TvScreenActivity{}\n\tif reqDto.ActivityType != nil {\n\t\tactivity.ActivityType = reqDto.ActivityType\n\t}\n\tif reqDto.StartTime != nil {\n\t\tactivity.StartTime = reqDto.StartTime\n\t}\n\tif reqDto.EndTime != nil {\n\t\tactivity.EndTime = reqDto.EndTime\n\t}\n\tif reqDto.ProductInfo != nil {\n\t\tactivity.ProductInfo = reqDto.ProductInfo\n\t}\n\n\terr = tvScreenActivityService.CreateTvScreenActivity(ctx, &activity)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, tvScreenActivityTransfer.PoToVo(activity))\n}\n\n// @Summary 更新电视屏幕活动\n// @Description 更新电视屏幕活动\n// @Tags 电视屏幕活动\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateTvScreenActivityReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.TvScreenActivityVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/tv-screen-activity/update [post]\nfunc (controller *TvScreenActivityController) UpdateTvScreenActivity(ctx *gin.Context) {\n\treqDto := req.UpdateTvScreenActivityReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\n\tactivity, err := tvScreenActivityService.FindTvScreenActivityById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tif reqDto.ActivityType != nil {\n\t\tactivity.ActivityType = reqDto.ActivityType\n\t}\n\tif reqDto.StartTime != nil {\n\t\tactivity.StartTime = reqDto.StartTime\n\t}\n\tif reqDto.EndTime != nil {\n\t\tactivity.EndTime = reqDto.EndTime\n\t}\n\tif reqDto.ProductInfo != nil {\n\t\tactivity.ProductInfo = reqDto.ProductInfo\n\t}\n\n\terr = tvScreenActivityService.UpdateTvScreenActivity(ctx, activity)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, tvScreenActivityTransfer.PoToVo(*activity))\n}\n\n// @Summary 删除电视屏幕活动\n// @Description 删除电视屏幕活动\n// @Tags 电视屏幕活动\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteTvScreenActivityReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/tv-screen-activity/delete [post]\nfunc (controller *TvScreenActivityController) DeleteTvScreenActivity(ctx *gin.Context) {\n\treqDto := req.DeleteTvScreenActivityReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\terr = tvScreenActivityService.DeleteTvScreenActivity(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询电视屏幕活动\n// @Description 查询电视屏幕活动\n// @Tags 电视屏幕活动\n// @Accept json\n// @Produce json\n// @Param body body req.QueryTvScreenActivityReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.TvScreenActivityVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/tv-screen-activity/query [post]\nfunc (controller *TvScreenActivityController) QueryTvScreenActivities(ctx *gin.Context) {\n\treqDto := req.QueryTvScreenActivityReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tlist, err := tvScreenActivityService.FindAllTvScreenActivity(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tnewList := []vo.TvScreenActivityVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, tvScreenActivityTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询电视屏幕活动列表\n// @Description 查询电视屏幕活动列表\n// @Tags 电视屏幕活动\n// @Accept json\n// @Produce json\n// @Param body body req.QueryTvScreenActivityReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.TvScreenActivityVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/tv-screen-activity/list [post]\nfunc (controller *TvScreenActivityController) ListTvScreenActivities(ctx *gin.Context) {\n\treqDto := req.QueryTvScreenActivityReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\n\tlist, totalCount, err := tvScreenActivityService.FindAllTvScreenActivityWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tpage := vo.PageVO[[]vo.TvScreenActivityVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.TvScreenActivityVO{}\n\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, tvScreenActivityTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype TvScreenActivityRoute struct {\n}\n\nfunc (s *TvScreenActivityRoute) InitTvScreenActivityRouter(g *gin.Engine) {\n\ttvScreenActivityController := controller.TvScreenActivityController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/tv-screen-activity/add\", tvScreenActivityController.AddTvScreenActivity)       // add\n\t\troute.POST(\"/api/tv-screen-activity/update\", tvScreenActivityController.UpdateTvScreenActivity)   // update\n\t\troute.POST(\"/api/tv-screen-activity/delete\", tvScreenActivityController.DeleteTvScreenActivity)   // delete\n\t\troute.POST(\"/api/tv-screen-activity/query\", tvScreenActivityController.QueryTvScreenActivities)   // query\n\t\troute.POST(\"/api/tv-screen-activity/list\", tvScreenActivityController.ListTvScreenActivities)     // list\n\t}\n}\n"}]