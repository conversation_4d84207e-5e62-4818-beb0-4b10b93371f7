[{"po": "package po\n\n// SubtitleInfo 字幕信息实体\ntype SubtitleInfo struct {\n\tId          *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`           // ID\n\tContent     *string `gorm:\"column:content;type:text;default:''\" json:\"content\"`                // 内容\n\tScrollSpeed *int    `gorm:\"column:scroll_speed;type:int;default:0\" json:\"scrollSpeed\"`         // 滚动速度\n\tStayTime    *int    `gorm:\"column:stay_time;type:int;default:0\" json:\"stayTime\"`             // 停留时间\n\tRepeatCount *int    `gorm:\"column:repeat_count;type:int;default:0\" json:\"repeatCount\"`       // 重复次数\n\tLoopPlay    *bool   `gorm:\"column:loop_play;type:bool;default:false\" json:\"loopPlay\"`         // 是否循环播放\n\tCtime       *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                     // 创建时间戳\n\tUtime       *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                     // 更新时间戳\n\tState       *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                     // 状态值\n\tVersion     *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`                 // 版本号\n}\n\n// TableName 设置表名\nfunc (SubtitleInfo) TableName() string {\n\treturn \"subtitle_info\"\n}\n\nfunc (s SubtitleInfo) GetId() string {\n\treturn *s.Id\n}", "vo": "package vo\n\n// SubtitleInfoVO 字幕信息值对象\ntype SubtitleInfoVO struct {\n\tId          string `json:\"id\"`           // ID\n\tContent     string `json:\"content\"`      // 内容\n\tScrollSpeed int    `json:\"scrollSpeed\"`  // 滚动速度\n\tStayTime    int    `json:\"stayTime\"`     // 停留时间\n\tRepeatCount int    `json:\"repeatCount\"`  // 重复次数\n\tLoopPlay    bool   `json:\"loopPlay\"`     // 是否循环播放\n\tCtime       int64  `json:\"ctime\"`        // 创建时间戳\n\tUtime       int64  `json:\"utime\"`        // 更新时间戳\n\tState       int    `json:\"state\"`        // 状态值\n\tVersion     int    `json:\"version\"`      // 版本号\n}", "req_add": "package req\n\n// AddSubtitleInfoReqDto 创建字幕信息请求DTO\ntype AddSubtitleInfoReqDto struct {\n\tContent     *string `json:\"content\"`      // 内容\n\tScrollSpeed *int    `json:\"scrollSpeed\"`  // 滚动速度\n\tStayTime    *int    `json:\"stayTime\"`     // 停留时间\n\tRepeatCount *int    `json:\"repeatCount\"`  // 重复次数\n\tLoopPlay    *bool   `json:\"loopPlay\"`     // 是否循环播放\n}", "req_update": "package req\n\n// UpdateSubtitleInfoReqDto 更新字幕信息请求DTO\ntype UpdateSubtitleInfoReqDto struct {\n\tId          *string `json:\"id\"`           // ID\n\tContent     *string `json:\"content\"`      // 内容\n\tScrollSpeed *int    `json:\"scrollSpeed\"`  // 滚动速度\n\tStayTime    *int    `json:\"stayTime\"`     // 停留时间\n\tRepeatCount *int    `json:\"repeatCount\"`  // 重复次数\n\tLoopPlay    *bool   `json:\"loopPlay\"`     // 是否循环播放\n}", "req_delete": "package req\n\n// DeleteSubtitleInfoReqDto 删除字幕信息请求DTO\ntype DeleteSubtitleInfoReqDto struct {\n\tId *string `json:\"id\"` // ID\n}", "req_query": "package req\n\n// QuerySubtitleInfoReqDto 查询字幕信息请求DTO\ntype QuerySubtitleInfoReqDto struct {\n\tId          *string `json:\"id\"`           // ID\n\tContent     *string `json:\"content\"`      // 内容\n\tScrollSpeed *int    `json:\"scrollSpeed\"`  // 滚动速度\n\tStayTime    *int    `json:\"stayTime\"`     // 停留时间\n\tRepeatCount *int    `json:\"repeatCount\"`  // 重复次数\n\tLoopPlay    *bool   `json:\"loopPlay\"`     // 是否循环播放\n\tPageNum     *int    `json:\"pageNum\"`      // 页码\n\tPageSize    *int    `json:\"pageSize\"`     // 每页记录数\n}", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype SubtitleInfoTransfer struct {\n}\n\nfunc (transfer *SubtitleInfoTransfer) PoToVo(po po.SubtitleInfo) vo.SubtitleInfoVO {\n\tvo := vo.SubtitleInfoVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *SubtitleInfoTransfer) VoToPo(vo vo.SubtitleInfoVO) po.SubtitleInfo {\n\tpo := po.SubtitleInfo{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype SubtitleInfoService struct {\n}\n\nfunc (service *SubtitleInfoService) CreateSubtitleInfo(logCtx *gin.Context, subtitleInfo *po.SubtitleInfo) error {\n\treturn Save(subtitleInfo)\n}\n\nfunc (service *SubtitleInfoService) UpdateSubtitleInfo(logCtx *gin.Context, subtitleInfo *po.SubtitleInfo) error {\n\treturn Update(subtitleInfo)\n}\n\nfunc (service *SubtitleInfoService) DeleteSubtitleInfo(logCtx *gin.Context, id string) error {\n\treturn Delete(po.SubtitleInfo{Id: &id})\n}\n\nfunc (service *SubtitleInfoService) FindSubtitleInfoById(logCtx *gin.Context, id string) (subtitleInfo *po.SubtitleInfo, err error) {\n\tsubtitleInfo = &po.SubtitleInfo{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(subtitleInfo).Error\n\treturn\n}\n\nfunc (service *SubtitleInfoService) FindAllSubtitleInfo(logCtx *gin.Context, reqDto *req.QuerySubtitleInfoReqDto) (list *[]po.SubtitleInfo, err error) {\n\tdb := model.DBSlave.Self.Model(&po.SubtitleInfo{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Content != nil && *reqDto.Content != \"\" {\n\t\tdb = db.Where(\"content LIKE ?\", \"%\"+*reqDto.Content+\"%\")\n\t}\n\tif reqDto.ScrollSpeed != nil {\n\t\tdb = db.Where(\"scroll_speed=?\", *reqDto.ScrollSpeed)\n\t}\n\tif reqDto.StayTime != nil {\n\t\tdb = db.Where(\"stay_time=?\", *reqDto.StayTime)\n\t}\n\tif reqDto.RepeatCount != nil {\n\t\tdb = db.Where(\"repeat_count=?\", *reqDto.RepeatCount)\n\t}\n\tif reqDto.LoopPlay != nil {\n\t\tdb = db.Where(\"loop_play=?\", *reqDto.LoopPlay)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.SubtitleInfo{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *SubtitleInfoService) FindAllSubtitleInfoWithPagination(logCtx *gin.Context, reqDto *req.QuerySubtitleInfoReqDto) (list *[]po.SubtitleInfo, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.SubtitleInfo{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Content != nil && *reqDto.Content != \"\" {\n\t\tdb = db.Where(\"content LIKE ?\", \"%\"+*reqDto.Content+\"%\")\n\t}\n\tif reqDto.ScrollSpeed != nil {\n\t\tdb = db.Where(\"scroll_speed=?\", *reqDto.ScrollSpeed)\n\t}\n\tif reqDto.StayTime != nil {\n\t\tdb = db.Where(\"stay_time=?\", *reqDto.StayTime)\n\t}\n\tif reqDto.RepeatCount != nil {\n\t\tdb = db.Where(\"repeat_count=?\", *reqDto.RepeatCount)\n\t}\n\tif reqDto.LoopPlay != nil {\n\t\tdb = db.Where(\"loop_play=?\", *reqDto.LoopPlay)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.SubtitleInfo{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype SubtitleInfoController struct{}\n\nvar (\n\tsubtitleInfoService  = impl.SubtitleInfoService{}\n\tsubtitleInfoTransfer = transfer.SubtitleInfoTransfer{}\n)\n\n// @Summary 添加字幕信息\n// @Description 添加字幕信息\n// @Tags 字幕信息\n// @Accept json\n// @Produce json\n// @Param body body req.AddSubtitleInfoReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.SubtitleInfoVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/subtitle-info/add [post]\nfunc (controller *SubtitleInfoController) AddSubtitleInfo(ctx *gin.Context) {\n\treqDto := req.AddSubtitleInfoReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tsubtitleInfo := po.SubtitleInfo{}\n\tif reqDto.Content != nil {\n\t\tsubtitleInfo.Content = reqDto.Content\n\t}\n\tif reqDto.ScrollSpeed != nil {\n\t\tsubtitleInfo.ScrollSpeed = reqDto.ScrollSpeed\n\t}\n\tif reqDto.StayTime != nil {\n\t\tsubtitleInfo.StayTime = reqDto.StayTime\n\t}\n\tif reqDto.RepeatCount != nil {\n\t\tsubtitleInfo.RepeatCount = reqDto.RepeatCount\n\t}\n\tif reqDto.LoopPlay != nil {\n\t\tsubtitleInfo.LoopPlay = reqDto.LoopPlay\n\t}\n\n\terr = subtitleInfoService.CreateSubtitleInfo(ctx, &subtitleInfo)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, subtitleInfoTransfer.PoToVo(subtitleInfo))\n}\n\n// @Summary 更新字幕信息\n// @Description 更新字幕信息\n// @Tags 字幕信息\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateSubtitleInfoReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.SubtitleInfoVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/subtitle-info/update [post]\nfunc (controller *SubtitleInfoController) UpdateSubtitleInfo(ctx *gin.Context) {\n\treqDto := req.UpdateSubtitleInfoReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\n\tsubtitleInfo, err := subtitleInfoService.FindSubtitleInfoById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tif reqDto.Content != nil {\n\t\tsubtitleInfo.Content = reqDto.Content\n\t}\n\tif reqDto.ScrollSpeed != nil {\n\t\tsubtitleInfo.ScrollSpeed = reqDto.ScrollSpeed\n\t}\n\tif reqDto.StayTime != nil {\n\t\tsubtitleInfo.StayTime = reqDto.StayTime\n\t}\n\tif reqDto.RepeatCount != nil {\n\t\tsubtitleInfo.RepeatCount = reqDto.RepeatCount\n\t}\n\tif reqDto.LoopPlay != nil {\n\t\tsubtitleInfo.LoopPlay = reqDto.LoopPlay\n\t}\n\n\terr = subtitleInfoService.UpdateSubtitleInfo(ctx, subtitleInfo)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, subtitleInfoTransfer.PoToVo(*subtitleInfo))\n}\n\n// @Summary 删除字幕信息\n// @Description 删除字幕信息\n// @Tags 字幕信息\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteSubtitleInfoReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/subtitle-info/delete [post]\nfunc (controller *SubtitleInfoController) DeleteSubtitleInfo(ctx *gin.Context) {\n\treqDto := req.DeleteSubtitleInfoReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\terr = subtitleInfoService.DeleteSubtitleInfo(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询字幕信息\n// @Description 查询字幕信息\n// @Tags 字幕信息\n// @Accept json\n// @Produce json\n// @Param body body req.QuerySubtitleInfoReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.SubtitleInfoVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/subtitle-info/query [post]\nfunc (controller *SubtitleInfoController) QuerySubtitleInfos(ctx *gin.Context) {\n\treqDto := req.QuerySubtitleInfoReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tlist, err := subtitleInfoService.FindAllSubtitleInfo(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tnewList := []vo.SubtitleInfoVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, subtitleInfoTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询字幕信息列表\n// @Description 查询字幕信息列表\n// @Tags 字幕信息\n// @Accept json\n// @Produce json\n// @Param body body req.QuerySubtitleInfoReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.SubtitleInfoVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/subtitle-info/list [post]\nfunc (controller *SubtitleInfoController) ListSubtitleInfos(ctx *gin.Context) {\n\treqDto := req.QuerySubtitleInfoReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\n\tlist, totalCount, err := subtitleInfoService.FindAllSubtitleInfoWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tpage := vo.PageVO[[]vo.SubtitleInfoVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.SubtitleInfoVO{}\n\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, subtitleInfoTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype SubtitleInfoRoute struct {\n}\n\nfunc (s *SubtitleInfoRoute) InitSubtitleInfoRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tsubtitleInfoController := controller.SubtitleInfoController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/subtitle-info/add\", subtitleInfoController.AddSubtitleInfo)       // add\n\t\troute.POST(\"/api/subtitle-info/update\", subtitleInfoController.UpdateSubtitleInfo)   // update\n\t\troute.POST(\"/api/subtitle-info/delete\", subtitleInfoController.DeleteSubtitleInfo)   // delete\n\t\troute.POST(\"/api/subtitle-info/query\", subtitleInfoController.QuerySubtitleInfos)   // query\n\t\troute.POST(\"/api/subtitle-info/list\", subtitleInfoController.ListSubtitleInfos)     // list\n\t}\n}"}]