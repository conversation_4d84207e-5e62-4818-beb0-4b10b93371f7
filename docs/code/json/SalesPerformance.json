[{"po": "package po\n\n// SalesPerformance 销售业绩实体\ntype SalesPerformance struct {\n\tId          *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`          // 唯一id\n\tAmount      *int64  `gorm:\"column:amount;type:bigint;default:0\" json:\"amount\"`                // 销售金额\n\tEmployeeId  *string `gorm:\"column:employee_id;type:varchar(64);default:''\" json:\"employeeId\"`  // 销售员ID\n\tSaleDate    *int64  `gorm:\"column:sale_date;type:bigint;default:0\" json:\"saleDate\"`          // 销售日期\n\tProductType *string `gorm:\"column:product_type;type:varchar(64);default:''\" json:\"productType\"` // 销售产品类型\n\tQuantity    *int    `gorm:\"column:quantity;type:int;default:0\" json:\"quantity\"`                // 销售数量\n\tCtime       *int64  `gorm:\"column:ctime;type:bigint;default:0\" json:\"ctime\"`                  // 创建时间\n\tUtime       *int64  `gorm:\"column:utime;type:bigint;default:0\" json:\"utime\"`                  // 更新时间\n\tState       *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                      // 状态\n\tVersion     *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`                  // 版本号\n}\n\n// TableName 设置表名\nfunc (SalesPerformance) TableName() string {\n\treturn \"sales_performance\"\n}\n\nfunc (s SalesPerformance) GetId() string {\n\treturn *s.Id\n}", "vo": "package vo\n\n// SalesPerformanceVO 销售业绩值对象\ntype SalesPerformanceVO struct {\n\tId          string `json:\"id\"`          // 唯一id\n\tAmount      int64  `json:\"amount\"`      // 销售金额\n\tEmployeeId  string `json:\"employeeId\"`  // 销售员ID\n\tSaleDate    int64  `json:\"saleDate\"`    // 销售日期\n\tProductType string `json:\"productType\"` // 销售产品类型\n\tQuantity    int    `json:\"quantity\"`    // 销售数量\n\tCtime       int64  `json:\"ctime\"`       // 创建时间\n\tUtime       int64  `json:\"utime\"`       // 更新时间\n\tState       int    `json:\"state\"`       // 状态\n\tVersion     int    `json:\"version\"`     // 版本号\n}", "req_add": "package req\n\n// AddSalesPerformanceReqDto 创建销售业绩请求DTO\ntype AddSalesPerformanceReqDto struct {\n\tAmount      *int64  `json:\"amount\"`      // 销售金额\n\tEmployeeId  *string `json:\"employeeId\"`  // 销售员ID\n\tSaleDate    *int64  `json:\"saleDate\"`    // 销售日期\n\tProductType *string `json:\"productType\"` // 销售产品类型\n\tQuantity    *int    `json:\"quantity\"`    // 销售数量\n}", "req_update": "package req\n\n// UpdateSalesPerformanceReqDto 更新销售业绩请求DTO\ntype UpdateSalesPerformanceReqDto struct {\n\tId          *string `json:\"id\"`          // 唯一id\n\tAmount      *int64  `json:\"amount\"`      // 销售金额\n\tEmployeeId  *string `json:\"employeeId\"`  // 销售员ID\n\tSaleDate    *int64  `json:\"saleDate\"`    // 销售日期\n\tProductType *string `json:\"productType\"` // 销售产品类型\n\tQuantity    *int    `json:\"quantity\"`    // 销售数量\n}", "req_delete": "package req\n\n// DeleteSalesPerformanceReqDto 删除销售业绩请求DTO\ntype DeleteSalesPerformanceReqDto struct {\n\tId *string `json:\"id\"` // 唯一id\n}", "req_query": "package req\n\n// QuerySalesPerformanceReqDto 查询销售业绩请求DTO\ntype QuerySalesPerformanceReqDto struct {\n\tId          *string `json:\"id\"`          // 唯一id\n\tAmount      *int64  `json:\"amount\"`      // 销售金额\n\tEmployeeId  *string `json:\"employeeId\"`  // 销售员ID\n\tSaleDate    *int64  `json:\"saleDate\"`    // 销售日期\n\tProductType *string `json:\"productType\"` // 销售产品类型\n\tQuantity    *int    `json:\"quantity\"`    // 销售数量\n\tPageNum     *int    `json:\"pageNum\"`     // 页码\n\tPageSize    *int    `json:\"pageSize\"`    // 每页记录数\n}", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype SalesPerformanceTransfer struct {\n}\n\nfunc (transfer *SalesPerformanceTransfer) PoToVo(po po.SalesPerformance) vo.SalesPerformanceVO {\n\tvo := vo.SalesPerformanceVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *SalesPerformanceTransfer) VoToPo(vo vo.SalesPerformanceVO) po.SalesPerformance {\n\tpo := po.SalesPerformance{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype SalesPerformanceService struct {\n}\n\nfunc (service *SalesPerformanceService) CreateSalesPerformance(logCtx *gin.Context, salesPerformance *po.SalesPerformance) error {\n\treturn Save(salesPerformance)\n}\n\nfunc (service *SalesPerformanceService) UpdateSalesPerformance(logCtx *gin.Context, salesPerformance *po.SalesPerformance) error {\n\treturn Update(salesPerformance)\n}\n\nfunc (service *SalesPerformanceService) DeleteSalesPerformance(logCtx *gin.Context, id string) error {\n\treturn Delete(po.SalesPerformance{Id: &id})\n}\n\nfunc (service *SalesPerformanceService) FindSalesPerformanceById(logCtx *gin.Context, id string) (salesPerformance *po.SalesPerformance, err error) {\n\tsalesPerformance = &po.SalesPerformance{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(salesPerformance).Error\n\treturn\n}\n\nfunc (service *SalesPerformanceService) FindAllSalesPerformance(logCtx *gin.Context, reqDto *req.QuerySalesPerformanceReqDto) (list *[]po.SalesPerformance, err error) {\n\tdb := model.DBSlave.Self.Model(&po.SalesPerformance{})\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.EmployeeId != nil && *reqDto.EmployeeId != \"\" {\n\t\tdb = db.Where(\"employee_id=?\", *reqDto.EmployeeId)\n\t}\n\tif reqDto.ProductType != nil && *reqDto.ProductType != \"\" {\n\t\tdb = db.Where(\"product_type=?\", *reqDto.ProductType)\n\t}\n\tif reqDto.Amount != nil {\n\t\tdb = db.Where(\"amount=?\", *reqDto.Amount)\n\t}\n\tif reqDto.SaleDate != nil {\n\t\tdb = db.Where(\"sale_date=?\", *reqDto.SaleDate)\n\t}\n\tif reqDto.Quantity != nil {\n\t\tdb = db.Where(\"quantity=?\", *reqDto.Quantity)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.SalesPerformance{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *SalesPerformanceService) FindAllSalesPerformanceWithPagination(logCtx *gin.Context, reqDto *req.QuerySalesPerformanceReqDto) (list *[]po.SalesPerformance, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.SalesPerformance{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.EmployeeId != nil && *reqDto.EmployeeId != \"\" {\n\t\tdb = db.Where(\"employee_id=?\", *reqDto.EmployeeId)\n\t}\n\tif reqDto.ProductType != nil && *reqDto.ProductType != \"\" {\n\t\tdb = db.Where(\"product_type=?\", *reqDto.ProductType)\n\t}\n\tif reqDto.Amount != nil {\n\t\tdb = db.Where(\"amount=?\", *reqDto.Amount)\n\t}\n\tif reqDto.SaleDate != nil {\n\t\tdb = db.Where(\"sale_date=?\", *reqDto.SaleDate)\n\t}\n\tif reqDto.Quantity != nil {\n\t\tdb = db.Where(\"quantity=?\", *reqDto.Quantity)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.SalesPerformance{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype SalesPerformanceController struct{}\n\nvar (\n\tsalesPerformanceService  = impl.SalesPerformanceService{}\n\tsalesPerformanceTransfer = transfer.SalesPerformanceTransfer{}\n)\n\n// @Summary 添加销售业绩\n// @Description 添加销售业绩\n// @Tags 销售业绩\n// @Accept json\n// @Produce json\n// @Param body body req.AddSalesPerformanceReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.SalesPerformanceVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/sales-performance/add [post]\nfunc (controller *SalesPerformanceController) AddSalesPerformance(ctx *gin.Context) {\n\treqDto := req.AddSalesPerformanceReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tsalesPerformance := po.SalesPerformance{}\n\tif reqDto.Amount != nil {\n\t\tsalesPerformance.Amount = reqDto.Amount\n\t}\n\tif reqDto.EmployeeId != nil {\n\t\tsalesPerformance.EmployeeId = reqDto.EmployeeId\n\t}\n\tif reqDto.SaleDate != nil {\n\t\tsalesPerformance.SaleDate = reqDto.SaleDate\n\t}\n\tif reqDto.ProductType != nil {\n\t\tsalesPerformance.ProductType = reqDto.ProductType\n\t}\n\tif reqDto.Quantity != nil {\n\t\tsalesPerformance.Quantity = reqDto.Quantity\n\t}\n\n\terr = salesPerformanceService.CreateSalesPerformance(ctx, &salesPerformance)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, salesPerformanceTransfer.PoToVo(salesPerformance))\n}\n\n// @Summary 更新销售业绩\n// @Description 更新销售业绩\n// @Tags 销售业绩\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateSalesPerformanceReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.SalesPerformanceVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/sales-performance/update [post]\nfunc (controller *SalesPerformanceController) UpdateSalesPerformance(ctx *gin.Context) {\n\treqDto := req.UpdateSalesPerformanceReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\n\tsalesPerformance, err := salesPerformanceService.FindSalesPerformanceById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tif reqDto.Amount != nil {\n\t\tsalesPerformance.Amount = reqDto.Amount\n\t}\n\tif reqDto.EmployeeId != nil {\n\t\tsalesPerformance.EmployeeId = reqDto.EmployeeId\n\t}\n\tif reqDto.SaleDate != nil {\n\t\tsalesPerformance.SaleDate = reqDto.SaleDate\n\t}\n\tif reqDto.ProductType != nil {\n\t\tsalesPerformance.ProductType = reqDto.ProductType\n\t}\n\tif reqDto.Quantity != nil {\n\t\tsalesPerformance.Quantity = reqDto.Quantity\n\t}\n\n\terr = salesPerformanceService.UpdateSalesPerformance(ctx, salesPerformance)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, salesPerformanceTransfer.PoToVo(*salesPerformance))\n}\n\n// @Summary 删除销售业绩\n// @Description 删除销售业绩\n// @Tags 销售业绩\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteSalesPerformanceReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/sales-performance/delete [post]\nfunc (controller *SalesPerformanceController) DeleteSalesPerformance(ctx *gin.Context) {\n\treqDto := req.DeleteSalesPerformanceReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = salesPerformanceService.DeleteSalesPerformance(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询销售业绩\n// @Description 查询销售业绩\n// @Tags 销售业绩\n// @Accept json\n// @Produce json\n// @Param body body req.QuerySalesPerformanceReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.SalesPerformanceVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/sales-performance/query [post]\nfunc (controller *SalesPerformanceController) QuerySalesPerformances(ctx *gin.Context) {\n\treqDto := req.QuerySalesPerformanceReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := salesPerformanceService.FindAllSalesPerformance(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.SalesPerformanceVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, salesPerformanceTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询销售业绩列表\n// @Description 查询销售业绩列表\n// @Tags 销售业绩\n// @Accept json\n// @Produce json\n// @Param body body req.QuerySalesPerformanceReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.SalesPerformanceVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/sales-performance/list [post]\nfunc (controller *SalesPerformanceController) ListSalesPerformances(ctx *gin.Context) {\n\treqDto := req.QuerySalesPerformanceReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := salesPerformanceService.FindAllSalesPerformanceWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.SalesPerformanceVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.SalesPerformanceVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, salesPerformanceTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype SalesPerformanceRoute struct {\n}\n\nfunc (s *SalesPerformanceRoute) InitSalesPerformanceRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tsalesPerformanceController := controller.SalesPerformanceController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/sales-performance/add\", salesPerformanceController.AddSalesPerformance)       // add\n\t\troute.POST(\"/api/sales-performance/update\", salesPerformanceController.UpdateSalesPerformance)   // update\n\t\troute.POST(\"/api/sales-performance/delete\", salesPerformanceController.DeleteSalesPerformance)   // delete\n\t\troute.POST(\"/api/sales-performance/query\", salesPerformanceController.QuerySalesPerformances)    // query\n\t\troute.POST(\"/api/sales-performance/list\", salesPerformanceController.ListSalesPerformances)     // list\n\t}\n}"}]