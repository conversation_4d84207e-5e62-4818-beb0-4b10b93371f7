[{"po": "package po\n\n// RoomGreeting 房间问候语实体\ntype RoomGreeting struct {\n\tId                  *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`                     // ID\n\tContent             *string `gorm:\"column:content;type:varchar(255);default:''\" json:\"content\"`                 // 问候内容\n\tApplicableRoomTypes *string `gorm:\"column:applicable_room_types;type:varchar(255);default:''\" json:\"applicableRoomTypes\"` // 适用房型\n\tCtime              *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                               // 创建时间戳\n\tUtime              *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                               // 更新时间戳\n\tState              *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                               // 状态值\n\tVersion            *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`                           // 版本号\n}\n\n// TableName 设置表名\nfunc (RoomGreeting) TableName() string {\n\treturn \"room_greeting\"\n}\n\nfunc (r RoomGreeting) GetId() string {\n\treturn *r.Id\n}", "vo": "package vo\n\n// RoomGreetingVO 房间问候语值对象\ntype RoomGreetingVO struct {\n\tId                  string `json:\"id\"`                  // ID\n\tContent             string `json:\"content\"`             // 问候内容\n\tApplicableRoomTypes string `json:\"applicableRoomTypes\"` // 适用房型\n\tCtime               int64  `json:\"ctime\"`               // 创建时间戳\n\tUtime               int64  `json:\"utime\"`               // 更新时间戳\n\tState               int    `json:\"state\"`               // 状态值\n\tVersion             int    `json:\"version\"`             // 版本号\n}", "req_add": "package req\n\n// AddRoomGreetingReqDto 创建房间问候语请求DTO\ntype AddRoomGreetingReqDto struct {\n\tContent             *string `json:\"content\"`             // 问候内容\n\tApplicableRoomTypes *string `json:\"applicableRoomTypes\"` // 适用房型\n}", "req_update": "package req\n\ntype UpdateRoomGreetingReqDto struct {\n\tId                  *string `json:\"id\"`                  // ID\n\tContent             *string `json:\"content\"`             // 问候内容\n\tApplicableRoomTypes *string `json:\"applicableRoomTypes\"` // 适用房型\n}", "req_delete": "package req\n\ntype DeleteRoomGreetingReqDto struct {\n\tId *string `json:\"id\"` // ID\n}", "req_query": "package req\n\ntype QueryRoomGreetingReqDto struct {\n\tId                  *string `json:\"id\"`                  // ID\n\tContent             *string `json:\"content\"`             // 问候内容\n\tApplicableRoomTypes *string `json:\"applicableRoomTypes\"` // 适用房型\n\tPageNum             *int    `json:\"pageNum\"`             // 页码\n\tPageSize            *int    `json:\"pageSize\"`            // 每页记录数\n}", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype RoomGreetingTransfer struct {\n}\n\nfunc (transfer *RoomGreetingTransfer) PoToVo(po po.RoomGreeting) vo.RoomGreetingVO {\n\tvo := vo.RoomGreetingVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *RoomGreetingTransfer) VoToPo(vo vo.RoomGreetingVO) po.RoomGreeting {\n\tpo := po.RoomGreeting{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype RoomGreetingService struct {\n}\n\nfunc (service *RoomGreetingService) CreateRoomGreeting(logCtx *gin.Context, roomGreeting *po.RoomGreeting) error {\n\treturn Save(roomGreeting)\n}\n\nfunc (service *RoomGreetingService) UpdateRoomGreeting(logCtx *gin.Context, roomGreeting *po.RoomGreeting) error {\n\treturn Update(roomGreeting)\n}\n\nfunc (service *RoomGreetingService) DeleteRoomGreeting(logCtx *gin.Context, id string) error {\n\treturn Delete(po.RoomGreeting{Id: &id})\n}\n\nfunc (service *RoomGreetingService) FindRoomGreetingById(logCtx *gin.Context, id string) (roomGreeting *po.RoomGreeting, err error) {\n\troomGreeting = &po.RoomGreeting{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(roomGreeting).Error\n\treturn\n}\n\nfunc (service *RoomGreetingService) FindAllRoomGreeting(logCtx *gin.Context, reqDto *req.QueryRoomGreetingReqDto) (list *[]po.RoomGreeting, err error) {\n\tdb := model.DBSlave.Self.Model(&po.RoomGreeting{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Content != nil && *reqDto.Content != \"\" {\n\t\tdb = db.Where(\"content LIKE ?\", \"%\"+*reqDto.Content+\"%\")\n\t}\n\tif reqDto.ApplicableRoomTypes != nil && *reqDto.ApplicableRoomTypes != \"\" {\n\t\tdb = db.Where(\"applicable_room_types=?\", *reqDto.ApplicableRoomTypes)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.RoomGreeting{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *RoomGreetingService) FindAllRoomGreetingWithPagination(logCtx *gin.Context, reqDto *req.QueryRoomGreetingReqDto) (list *[]po.RoomGreeting, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.RoomGreeting{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Content != nil && *reqDto.Content != \"\" {\n\t\tdb = db.Where(\"content LIKE ?\", \"%\"+*reqDto.Content+\"%\")\n\t}\n\tif reqDto.ApplicableRoomTypes != nil && *reqDto.ApplicableRoomTypes != \"\" {\n\t\tdb = db.Where(\"applicable_room_types=?\", *reqDto.ApplicableRoomTypes)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.RoomGreeting{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype RoomGreetingController struct{}\n\nvar (\n\troomGreetingService  = impl.RoomGreetingService{}\n\troomGreetingTransfer = transfer.RoomGreetingTransfer{}\n)\n\n// @Summary 添加房间问候语\n// @Description 添加房间问候语\n// @Tags 房间问候语\n// @Accept json\n// @Produce json\n// @Param body body req.AddRoomGreetingReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.RoomGreetingVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/room-greeting/add [post]\nfunc (controller *RoomGreetingController) AddRoomGreeting(ctx *gin.Context) {\n\treqDto := req.AddRoomGreetingReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\troomGreeting := po.RoomGreeting{}\n\tif reqDto.Content != nil {\n\t\troomGreeting.Content = reqDto.Content\n\t}\n\tif reqDto.ApplicableRoomTypes != nil {\n\t\troomGreeting.ApplicableRoomTypes = reqDto.ApplicableRoomTypes\n\t}\n\terr = roomGreetingService.CreateRoomGreeting(ctx, &roomGreeting)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, roomGreetingTransfer.PoToVo(roomGreeting))\n}\n\n// @Summary 更新房间问候语\n// @Description 更新房间问候语\n// @Tags 房间问候语\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateRoomGreetingReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.RoomGreetingVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/room-greeting/update [post]\nfunc (controller *RoomGreetingController) UpdateRoomGreeting(ctx *gin.Context) {\n\treqDto := req.UpdateRoomGreetingReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\troomGreeting, err := roomGreetingService.FindRoomGreetingById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Content != nil {\n\t\troomGreeting.Content = reqDto.Content\n\t}\n\tif reqDto.ApplicableRoomTypes != nil {\n\t\troomGreeting.ApplicableRoomTypes = reqDto.ApplicableRoomTypes\n\t}\n\terr = roomGreetingService.UpdateRoomGreeting(ctx, roomGreeting)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, roomGreetingTransfer.PoToVo(*roomGreeting))\n}\n\n// @Summary 删除房间问候语\n// @Description 删除房间问候语\n// @Tags 房间问候语\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteRoomGreetingReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/room-greeting/delete [post]\nfunc (controller *RoomGreetingController) DeleteRoomGreeting(ctx *gin.Context) {\n\treqDto := req.DeleteRoomGreetingReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = roomGreetingService.DeleteRoomGreeting(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询房间问候语\n// @Description 查询房间问候语\n// @Tags 房间问候语\n// @Accept json\n// @Produce json\n// @Param body body req.QueryRoomGreetingReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.RoomGreetingVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/room-greeting/query [post]\nfunc (controller *RoomGreetingController) QueryRoomGreetings(ctx *gin.Context) {\n\treqDto := req.QueryRoomGreetingReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := roomGreetingService.FindAllRoomGreeting(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.RoomGreetingVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, roomGreetingTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询房间问候语列表\n// @Description 查询房间问候语列表\n// @Tags 房间问候语\n// @Accept json\n// @Produce json\n// @Param body body req.QueryRoomGreetingReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.RoomGreetingVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/room-greeting/list [post]\nfunc (a *RoomGreetingController) ListRoomGreetings(ctx *gin.Context) {\n\treqDto := req.QueryRoomGreetingReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := roomGreetingService.FindAllRoomGreetingWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.RoomGreetingVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.RoomGreetingVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, roomGreetingTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype RoomGreetingRoute struct {\n}\n\nfunc (s *RoomGreetingRoute) InitRoomGreetingRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\troomGreetingController := controller.RoomGreetingController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/room-greeting/add\", roomGreetingController.AddRoomGreeting)       //add\n\t\troute.POST(\"/api/room-greeting/update\", roomGreetingController.UpdateRoomGreeting)   //update\n\t\troute.POST(\"/api/room-greeting/delete\", roomGreetingController.DeleteRoomGreeting)   //delete\n\t\troute.POST(\"/api/room-greeting/query\", roomGreetingController.QueryRoomGreetings)    //query\n\t\troute.POST(\"/api/room-greeting/list\", roomGreetingController.ListRoomGreetings)     //list\n\t}\n}"}]