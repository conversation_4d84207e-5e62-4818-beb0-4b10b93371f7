[{"po": "package po\n\n// Voucher 凭证实体\ntype Voucher struct {\n\tId          *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`          // 唯一ID\n\tVoucherId   *string `gorm:\"column:voucher_id;type:varchar(64);default:''\" json:\"voucherId\"`   // 凭证ID\n\tVoucherCode *string `gorm:\"column:voucher_code;type:varchar(64);default:''\" json:\"voucherCode\"` // 凭证码\n\tPlanId      *string `gorm:\"column:plan_id;type:varchar(64);default:''\" json:\"planId\"`       // 计划ID\n\tStatus      *string `gorm:\"column:status;type:varchar(64);default:''\" json:\"status\"`       // 状态\n\tCtime       *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                // 创建时间戳\n\tUtime       *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                // 更新时间戳\n\tState       *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                // 状态值\n\tVersion     *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`            // 版本号\n}\n\n// TableName 设置表名\nfunc (Voucher) TableName() string {\n\treturn \"voucher\"\n}\n\nfunc (v Voucher) GetId() string {\n\treturn *v.Id\n}\n", "vo": "package vo\n\n// VoucherVO 凭证信息值对象\ntype VoucherVO struct {\n\tId          string `json:\"id\"`          // 唯一ID\n\tVoucherId   string `json:\"voucherId\"`   // 凭证ID\n\tVoucherCode string `json:\"voucherCode\"` // 凭证码\n\tPlanId      string `json:\"planId\"`      // 计划ID\n\tStatus      string `json:\"status\"`      // 状态\n\tCtime       int64  `json:\"ctime\"`       // 创建时间戳\n\tUtime       int64  `json:\"utime\"`       // 更新时间戳\n\tState       int    `json:\"state\"`       // 状态值\n\tVersion     int    `json:\"version\"`     // 版本号\n}\n", "req_add": "package req\n\n// AddVoucherReqDto 创建凭证请求DTO\ntype AddVoucherReqDto struct {\n\tVoucherId   *string `json:\"voucherId\"`   // 凭证ID\n\tVoucherCode *string `json:\"voucherCode\"` // 凭证码\n\tPlanId      *string `json:\"planId\"`      // 计划ID\n\tStatus      *string `json:\"status\"`      // 状态\n}\n", "req_update": "package req\n\ntype UpdateVoucherReqDto struct {\n\tId          *string `json:\"id\"`          // 唯一ID\n\tVoucherId   *string `json:\"voucherId\"`   // 凭证ID\n\tVoucherCode *string `json:\"voucherCode\"` // 凭证码\n\tPlanId      *string `json:\"planId\"`      // 计划ID\n\tStatus      *string `json:\"status\"`      // 状态\n}\n", "req_delete": "package req\n\ntype DeleteVoucherReqDto struct {\n\tId *string `json:\"id\"` // 唯一ID\n}\n", "req_query": "package req\n\ntype QueryVoucherReqDto struct {\n\tId          *string `json:\"id\"`          // 唯一ID\n\tVoucherId   *string `json:\"voucherId\"`   // 凭证ID\n\tVoucherCode *string `json:\"voucherCode\"` // 凭证码\n\tPlanId      *string `json:\"planId\"`      // 计划ID\n\tStatus      *string `json:\"status\"`      // 状态\n\tPageNum     *int    `json:\"pageNum\"`     // 页码\n\tPageSize    *int    `json:\"pageSize\"`    // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype VoucherTransfer struct {\n}\n\nfunc (transfer *VoucherTransfer) PoToVo(po po.Voucher) vo.VoucherVO {\n\tvo := vo.VoucherVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *VoucherTransfer) VoToPo(vo vo.VoucherVO) po.Voucher {\n\tpo := po.Voucher{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype VoucherService struct {\n}\n\nfunc (service *VoucherService) CreateVoucher(logCtx *gin.Context, voucher *po.Voucher) error {\n\treturn Save(voucher)\n}\n\nfunc (service *VoucherService) UpdateVoucher(logCtx *gin.Context, voucher *po.Voucher) error {\n\treturn Update(voucher)\n}\n\nfunc (service *VoucherService) DeleteVoucher(logCtx *gin.Context, id string) error {\n\treturn Delete(po.Voucher{Id: &id})\n}\n\nfunc (service *VoucherService) FindVoucherById(logCtx *gin.Context, id string) (voucher *po.Voucher, err error) {\n\tvoucher = &po.Voucher{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(voucher).Error\n\treturn\n}\n\nfunc (service *VoucherService) FindAllVoucher(logCtx *gin.Context, reqDto *req.QueryVoucherReqDto) (list *[]po.Voucher, err error) {\n\tdb := model.DBSlave.Self.Model(&po.Voucher{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.VoucherId != nil && *reqDto.VoucherId != \"\" {\n\t\tdb = db.Where(\"voucher_id=?\", *reqDto.VoucherId)\n\t}\n\tif reqDto.VoucherCode != nil && *reqDto.VoucherCode != \"\" {\n\t\tdb = db.Where(\"voucher_code=?\", *reqDto.VoucherCode)\n\t}\n\tif reqDto.PlanId != nil && *reqDto.PlanId != \"\" {\n\t\tdb = db.Where(\"plan_id=?\", *reqDto.PlanId)\n\t}\n\tif reqDto.Status != nil && *reqDto.Status != \"\" {\n\t\tdb = db.Where(\"status=?\", *reqDto.Status)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.Voucher{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *VoucherService) FindAllVoucherWithPagination(logCtx *gin.Context, reqDto *req.QueryVoucherReqDto) (list *[]po.Voucher, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.Voucher{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.VoucherId != nil && *reqDto.VoucherId != \"\" {\n\t\tdb = db.Where(\"voucher_id=?\", *reqDto.VoucherId)\n\t}\n\tif reqDto.VoucherCode != nil && *reqDto.VoucherCode != \"\" {\n\t\tdb = db.Where(\"voucher_code=?\", *reqDto.VoucherCode)\n\t}\n\tif reqDto.PlanId != nil && *reqDto.PlanId != \"\" {\n\t\tdb = db.Where(\"plan_id=?\", *reqDto.PlanId)\n\t}\n\tif reqDto.Status != nil && *reqDto.Status != \"\" {\n\t\tdb = db.Where(\"status=?\", *reqDto.Status)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.Voucher{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype VoucherController struct{}\n\nvar (\n\tvoucherService  = impl.VoucherService{}\n\tvoucherTransfer = transfer.VoucherTransfer{}\n)\n\n// @Summary 添加凭证\n// @Description 添加凭证\n// @Tags 凭证\n// @Accept json\n// @Produce json\n// @Param body body req.AddVoucherReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.VoucherVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/voucher/add [post]\nfunc (controller *VoucherController) AddVoucher(ctx *gin.Context) {\n\treqDto := req.AddVoucherReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tvoucher := po.Voucher{}\n\tif reqDto.VoucherId != nil {\n\t\tvoucher.VoucherId = reqDto.VoucherId\n\t}\n\tif reqDto.VoucherCode != nil {\n\t\tvoucher.VoucherCode = reqDto.VoucherCode\n\t}\n\tif reqDto.PlanId != nil {\n\t\tvoucher.PlanId = reqDto.PlanId\n\t}\n\tif reqDto.Status != nil {\n\t\tvoucher.Status = reqDto.Status\n\t}\n\terr = voucherService.CreateVoucher(ctx, &voucher)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, voucherTransfer.PoToVo(voucher))\n}\n\n// @Summary 更新凭证\n// @Description 更新凭证\n// @Tags 凭证\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateVoucherReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.VoucherVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/voucher/update [post]\nfunc (controller *VoucherController) UpdateVoucher(ctx *gin.Context) {\n\treqDto := req.UpdateVoucherReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\tvoucher, err := voucherService.FindVoucherById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.VoucherId != nil {\n\t\tvoucher.VoucherId = reqDto.VoucherId\n\t}\n\tif reqDto.VoucherCode != nil {\n\t\tvoucher.VoucherCode = reqDto.VoucherCode\n\t}\n\tif reqDto.PlanId != nil {\n\t\tvoucher.PlanId = reqDto.PlanId\n\t}\n\tif reqDto.Status != nil {\n\t\tvoucher.Status = reqDto.Status\n\t}\n\terr = voucherService.UpdateVoucher(ctx, voucher)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, voucherTransfer.PoToVo(*voucher))\n}\n\n// @Summary 删除凭证\n// @Description 删除凭证\n// @Tags 凭证\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteVoucherReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/voucher/delete [post]\nfunc (controller *VoucherController) DeleteVoucher(ctx *gin.Context) {\n\treqDto := req.DeleteVoucherReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = voucherService.DeleteVoucher(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询凭证\n// @Description 查询凭证\n// @Tags 凭证\n// @Accept json\n// @Produce json\n// @Param body body req.QueryVoucherReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.VoucherVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/voucher/query [post]\nfunc (controller *VoucherController) QueryVouchers(ctx *gin.Context) {\n\treqDto := req.QueryVoucherReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := voucherService.FindAllVoucher(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.VoucherVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, voucherTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询凭证列表\n// @Description 查询凭证列表\n// @Tags 凭证\n// @Accept json\n// @Produce json\n// @Param body body req.QueryVoucherReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.VoucherVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/voucher/list [post]\nfunc (a *VoucherController) ListVouchers(ctx *gin.Context) {\n\treqDto := req.QueryVoucherReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := voucherService.FindAllVoucherWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.VoucherVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.VoucherVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, voucherTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype VoucherRoute struct {\n}\n\nfunc (s *VoucherRoute) InitVoucherRouter(g *gin.Engine) {\n\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tvoucherController := controller.VoucherController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/voucher/add\", voucherController.AddVoucher)    //add\n\t\troute.POST(\"/api/voucher/update\", voucherController.UpdateVoucher) //update\n\t\troute.POST(\"/api/voucher/delete\", voucherController.DeleteVoucher) //delete\n\t\troute.POST(\"/api/voucher/query\", voucherController.QueryVouchers)     //query\n\t\troute.POST(\"/api/voucher/list\", voucherController.ListVouchers)     //list\n\t}\n}\n"}]