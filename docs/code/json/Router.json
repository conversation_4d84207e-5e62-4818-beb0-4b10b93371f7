[{"po": "package po\n\n// Router 路由器实体\ntype Router struct {\n\tId          *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`           // ID\n\tDhcpEnabled *bool   `gorm:\"column:dhcp_enabled;type:bool;default:false\" json:\"dhcpEnabled\"` // 是否启用DHCP\n\tIpAddress   *string `gorm:\"column:ip_address;type:varchar(64);default:''\" json:\"ipAddress\"` // IP地址\n\tCtime       *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                   // 创建时间戳\n\tUtime       *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                   // 更新时间戳\n\tState       *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                   // 状态值\n\tVersion     *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`               // 版本号\n}\n\n// TableName 设置表名\nfunc (Router) TableName() string {\n\treturn \"router\"\n}\n\nfunc (r Router) GetId() string {\n\treturn *r.Id\n}", "vo": "package vo\n\n// RouterVO 路由器信息值对象\ntype RouterVO struct {\n\tId          string `json:\"id\"`          // ID\n\tDhcpEnabled bool   `json:\"dhcpEnabled\"` // 是否启用DHCP\n\tIpAddress   string `json:\"ipAddress\"`   // IP地址\n\tCtime       int64  `json:\"ctime\"`       // 创建时间戳\n\tUtime       int64  `json:\"utime\"`       // 更新时间戳\n\tState       int    `json:\"state\"`       // 状态值\n\tVersion     int    `json:\"version\"`     // 版本号\n}", "req_add": "package req\n\n// AddRouterReqDto 创建路由器请求DTO\ntype AddRouterReqDto struct {\n\tDhcpEnabled *bool   `json:\"dhcpEnabled\"` // 是否启用DHCP\n\tIpAddress   *string `json:\"ipAddress\"`   // IP地址\n}", "req_update": "package req\n\ntype UpdateRouterReqDto struct {\n\tId          *string `json:\"id\"`          // 路由器ID\n\tDhcpEnabled *bool   `json:\"dhcpEnabled\"` // 是否启用DHCP\n\tIpAddress   *string `json:\"ipAddress\"`   // IP地址\n}", "req_delete": "package req\n\ntype DeleteRouterReqDto struct {\n\tId *string `json:\"id\"` // 路由器ID\n}", "req_query": "package req\n\ntype QueryRouterReqDto struct {\n\tId          *string `json:\"id\"`          // 路由器ID\n\tDhcpEnabled *bool   `json:\"dhcpEnabled\"` // 是否启用DHCP\n\tIpAddress   *string `json:\"ipAddress\"`   // IP地址\n\tPageNum     *int    `json:\"pageNum\"`     // 页码\n\tPageSize    *int    `json:\"pageSize\"`    // 每页记录数\n}", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype RouterTransfer struct {\n}\n\nfunc (transfer *RouterTransfer) PoToVo(po po.Router) vo.RouterVO {\n\tvo := vo.RouterVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *RouterTransfer) VoToPo(vo vo.RouterVO) po.Router {\n\tpo := po.Router{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype RouterService struct {\n}\n\nfunc (service *RouterService) CreateRouter(logCtx *gin.Context, router *po.Router) error {\n\treturn Save(router)\n}\n\nfunc (service *RouterService) UpdateRouter(logCtx *gin.Context, router *po.Router) error {\n\treturn Update(router)\n}\n\nfunc (service *RouterService) DeleteRouter(logCtx *gin.Context, id string) error {\n\treturn Delete(po.Router{Id: &id})\n}\n\nfunc (service *RouterService) FindRouterById(logCtx *gin.Context, id string) (router *po.Router, err error) {\n\trouter = &po.Router{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(router).Error\n\treturn\n}\n\nfunc (service *RouterService) FindAllRouter(logCtx *gin.Context, reqDto *req.QueryRouterReqDto) (list *[]po.Router, err error) {\n\tdb := model.DBSlave.Self.Model(&po.Router{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.DhcpEnabled != nil {\n\t\tdb = db.Where(\"dhcp_enabled=?\", *reqDto.DhcpEnabled)\n\t}\n\tif reqDto.IpAddress != nil && *reqDto.IpAddress != \"\" {\n\t\tdb = db.Where(\"ip_address=?\", *reqDto.IpAddress)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.Router{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *RouterService) FindAllRouterWithPagination(logCtx *gin.Context, reqDto *req.QueryRouterReqDto) (list *[]po.Router, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.Router{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.DhcpEnabled != nil {\n\t\tdb = db.Where(\"dhcp_enabled=?\", *reqDto.DhcpEnabled)\n\t}\n\tif reqDto.IpAddress != nil && *reqDto.IpAddress != \"\" {\n\t\tdb = db.Where(\"ip_address=?\", *reqDto.IpAddress)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.Router{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype RouterController struct{}\n\nvar (\n\trouterService  = impl.RouterService{}\n\trouterTransfer = transfer.RouterTransfer{}\n)\n\n// @Summary 添加路由器\n// @Description 添加路由器\n// @Tags 路由器\n// @Accept json\n// @Produce json\n// @Param body body req.AddRouterReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.RouterVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/router/add [post]\nfunc (controller *RouterController) AddRouter(ctx *gin.Context) {\n\treqDto := req.AddRouterReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\trouter := po.Router{}\n\tif reqDto.DhcpEnabled != nil {\n\t\trouter.DhcpEnabled = reqDto.DhcpEnabled\n\t}\n\tif reqDto.IpAddress != nil {\n\t\trouter.IpAddress = reqDto.IpAddress\n\t}\n\terr = routerService.CreateRouter(ctx, &router)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, routerTransfer.PoToVo(router))\n}\n\n// @Summary 更新路由器\n// @Description 更新路由器\n// @Tags 路由器\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateRouterReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.RouterVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/router/update [post]\nfunc (controller *RouterController) UpdateRouter(ctx *gin.Context) {\n\treqDto := req.UpdateRouterReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\trouter, err := routerService.FindRouterById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.DhcpEnabled != nil {\n\t\trouter.DhcpEnabled = reqDto.DhcpEnabled\n\t}\n\tif reqDto.IpAddress != nil {\n\t\trouter.IpAddress = reqDto.IpAddress\n\t}\n\terr = routerService.UpdateRouter(ctx, router)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, routerTransfer.PoToVo(*router))\n}\n\n// @Summary 删除路由器\n// @Description 删除路由器\n// @Tags 路由器\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteRouterReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/router/delete [post]\nfunc (controller *RouterController) DeleteRouter(ctx *gin.Context) {\n\treqDto := req.DeleteRouterReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = routerService.DeleteRouter(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询路由器\n// @Description 查询路由器\n// @Tags 路由器\n// @Accept json\n// @Produce json\n// @Param body body req.QueryRouterReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.RouterVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/router/query [post]\nfunc (controller *RouterController) QueryRouters(ctx *gin.Context) {\n\treqDto := req.QueryRouterReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := routerService.FindAllRouter(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.RouterVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, routerTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询路由器列表\n// @Description 查询路由器列表\n// @Tags 路由器\n// @Accept json\n// @Produce json\n// @Param body body req.QueryRouterReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.RouterVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/router/list [post]\nfunc (a *RouterController) ListRouters(ctx *gin.Context) {\n\treqDto := req.QueryRouterReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := routerService.FindAllRouterWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.RouterVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.RouterVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, routerTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype RouterRoute struct {\n}\n\nfunc (s *RouterRoute) InitRouterRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\trouterController := controller.RouterController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/router/add\", routerController.AddRouter)    //add\n\t\troute.POST(\"/api/router/update\", routerController.UpdateRouter) //update\n\t\troute.POST(\"/api/router/delete\", routerController.DeleteRouter) //delete\n\t\troute.POST(\"/api/router/query\", routerController.QueryRouters)     //query\n\t\troute.POST(\"/api/router/list\", routerController.ListRouters)     //list\n\t}\n}"}]