[{"po": "package po\n\n// ShiftReport 班次报告实体\ntype ShiftReport struct {\n\tId                   *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`                    // 唯一ID\n\tOpenedTables         *int    `gorm:\"column:opened_tables;type:int;default:0\" json:\"openedTables\"`           // 开台数量\n\tSettledTables        *int    `gorm:\"column:settled_tables;type:int;default:0\" json:\"settledTables\"`         // 已结算台数\n\tUnsettledTables      *int    `gorm:\"column:unsettled_tables;type:int;default:0\" json:\"unsettledTables\"`     // 未结算台数\n\tOpenedAmount         *int64  `gorm:\"column:opened_amount;type:bigint;default:0\" json:\"openedAmount\"`         // 开台金额\n\tSettledAmount        *int64  `gorm:\"column:settled_amount;type:bigint;default:0\" json:\"settledAmount\"`       // 已结算金额\n\tUnsettledAmount      *int64  `gorm:\"column:unsettled_amount;type:bigint;default:0\" json:\"unsettledAmount\"`   // 未结算金额\n\tSoldProducts         *string `gorm:\"column:sold_products;type:text\" json:\"soldProducts\"`                     // 销售产品列表(JSON)\n\tSoldProductsAmount   *int64  `gorm:\"column:sold_products_amount;type:bigint;default:0\" json:\"soldProductsAmount\"` // 销售产品总金额\n\tGiftedProducts       *string `gorm:\"column:gifted_products;type:text\" json:\"giftedProducts\"`                 // 赠送产品列表(JSON)\n\tGiftedProductsAmount *int64  `gorm:\"column:gifted_products_amount;type:bigint;default:0\" json:\"giftedProductsAmount\"` // 赠送产品总金额\n\tReturnedProducts     *string `gorm:\"column:returned_products;type:text\" json:\"returnedProducts\"`             // 退货产品列表(JSON)\n\tReturnedProductsAmount *int64 `gorm:\"column:returned_products_amount;type:bigint;default:0\" json:\"returnedProductsAmount\"` // 退货产品总金额\n\tMemberRecharge       *int64  `gorm:\"column:member_recharge;type:bigint;default:0\" json:\"memberRecharge\"`     // 会员充值金额\n\tMemberConsumption    *int64  `gorm:\"column:member_consumption;type:bigint;default:0\" json:\"memberConsumption\"` // 会员消费金额\n\tPaymentMethodStats   *string `gorm:\"column:payment_method_stats;type:text\" json:\"paymentMethodStats\"`       // 支付方式统计(JSON)\n\tShiftTime            *int64  `gorm:\"column:shift_time;type:bigint;default:0\" json:\"shiftTime\"`              // 班次时间\n\tCtime                *int64  `gorm:\"column:ctime;type:bigint;default:0\" json:\"ctime\"`                      // 创建时间\n\tUtime                *int64  `gorm:\"column:utime;type:bigint;default:0\" json:\"utime\"`                      // 更新时间\n\tState                *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                        // 状态\n\tVersion              *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`                    // 版本\n}\n\n// TableName 设置表名\nfunc (ShiftReport) TableName() string {\n\treturn \"shift_report\"\n}\n\nfunc (s ShiftReport) GetId() string {\n\treturn *s.Id\n}\n", "vo": "package vo\n\n// ShiftReportVO 班次报告值对象\ntype ShiftReportVO struct {\n\tId                    string `json:\"id\"`                    // 唯一ID\n\tOpenedTables          int    `json:\"openedTables\"`          // 开台数量\n\tSettledTables         int    `json:\"settledTables\"`         // 已结算台数\n\tUnsettledTables       int    `json:\"unsettledTables\"`       // 未结算台数\n\tOpenedAmount          int64  `json:\"openedAmount\"`          // 开台金额\n\tSettledAmount         int64  `json:\"settledAmount\"`         // 已结算金额\n\tUnsettledAmount       int64  `json:\"unsettledAmount\"`       // 未结算金额\n\tSoldProducts          string `json:\"soldProducts\"`          // 销售产品列表(JSON)\n\tSoldProductsAmount    int64  `json:\"soldProductsAmount\"`    // 销售产品总金额\n\tGiftedProducts        string `json:\"giftedProducts\"`        // 赠送产品列表(JSON)\n\tGiftedProductsAmount  int64  `json:\"giftedProductsAmount\"`  // 赠送产品总金额\n\tReturnedProducts      string `json:\"returnedProducts\"`      // 退货产品列表(JSON)\n\tReturnedProductsAmount int64  `json:\"returnedProductsAmount\"` // 退货产品总金额\n\tMemberRecharge        int64  `json:\"memberRecharge\"`        // 会员充值金额\n\tMemberConsumption     int64  `json:\"memberConsumption\"`     // 会员消费金额\n\tPaymentMethodStats    string `json:\"paymentMethodStats\"`    // 支付方式统计(JSON)\n\tShiftTime             int64  `json:\"shiftTime\"`             // 班次时间\n\tCtime                 int64  `json:\"ctime\"`                 // 创建时间\n\tUtime                 int64  `json:\"utime\"`                 // 更新时间\n\tState                 int    `json:\"state\"`                 // 状态\n\tVersion               int    `json:\"version\"`               // 版本\n}\n", "req_add": "package req\n\n// AddShiftReportReqDto 创建班次报告请求DTO\ntype AddShiftReportReqDto struct {\n\tOpenedTables          *int    `json:\"openedTables\"`          // 开台数量\n\tSettledTables         *int    `json:\"settledTables\"`         // 已结算台数\n\tUnsettledTables       *int    `json:\"unsettledTables\"`       // 未结算台数\n\tOpenedAmount          *int64  `json:\"openedAmount\"`          // 开台金额\n\tSettledAmount         *int64  `json:\"settledAmount\"`         // 已结算金额\n\tUnsettledAmount       *int64  `json:\"unsettledAmount\"`       // 未结算金额\n\tSoldProducts          *string `json:\"soldProducts\"`          // 销售产品列表(JSON)\n\tSoldProductsAmount    *int64  `json:\"soldProductsAmount\"`    // 销售产品总金额\n\tGiftedProducts        *string `json:\"giftedProducts\"`        // 赠送产品列表(JSON)\n\tGiftedProductsAmount  *int64  `json:\"giftedProductsAmount\"`  // 赠送产品总金额\n\tReturnedProducts      *string `json:\"returnedProducts\"`      // 退货产品列表(JSON)\n\tReturnedProductsAmount *int64  `json:\"returnedProductsAmount\"` // 退货产品总金额\n\tMemberRecharge        *int64  `json:\"memberRecharge\"`        // 会员充值金额\n\tMemberConsumption     *int64  `json:\"memberConsumption\"`     // 会员消费金额\n\tPaymentMethodStats    *string `json:\"paymentMethodStats\"`    // 支付方式统计(JSON)\n\tShiftTime             *int64  `json:\"shiftTime\"`             // 班次时间\n}\n", "req_update": "package req\n\n// UpdateShiftReportReqDto 更新班次报告请求DTO\ntype UpdateShiftReportReqDto struct {\n\tId                    *string `json:\"id\"`                    // 唯一ID\n\tOpenedTables          *int    `json:\"openedTables\"`          // 开台数量\n\tSettledTables         *int    `json:\"settledTables\"`         // 已结算台数\n\tUnsettledTables       *int    `json:\"unsettledTables\"`       // 未结算台数\n\tOpenedAmount          *int64  `json:\"openedAmount\"`          // 开台金额\n\tSettledAmount         *int64  `json:\"settledAmount\"`         // 已结算金额\n\tUnsettledAmount       *int64  `json:\"unsettledAmount\"`       // 未结算金额\n\tSoldProducts          *string `json:\"soldProducts\"`          // 销售产品列表(JSON)\n\tSoldProductsAmount    *int64  `json:\"soldProductsAmount\"`    // 销售产品总金额\n\tGiftedProducts        *string `json:\"giftedProducts\"`        // 赠送产品列表(JSON)\n\tGiftedProductsAmount  *int64  `json:\"giftedProductsAmount\"`  // 赠送产品总金额\n\tReturnedProducts      *string `json:\"returnedProducts\"`      // 退货产品列表(JSON)\n\tReturnedProductsAmount *int64  `json:\"returnedProductsAmount\"` // 退货产品总金额\n\tMemberRecharge        *int64  `json:\"memberRecharge\"`        // 会员充值金额\n\tMemberConsumption     *int64  `json:\"memberConsumption\"`     // 会员消费金额\n\tPaymentMethodStats    *string `json:\"paymentMethodStats\"`    // 支付方式统计(JSON)\n\tShiftTime             *int64  `json:\"shiftTime\"`             // 班次时间\n}\n", "req_delete": "package req\n\n// DeleteShiftReportReqDto 删除班次报告请求DTO\ntype DeleteShiftReportReqDto struct {\n\tId *string `json:\"id\"` // 唯一ID\n}\n", "req_query": "package req\n\n// QueryShiftReportReqDto 查询班次报告请求DTO\ntype QueryShiftReportReqDto struct {\n\tId                    *string `json:\"id\"`                    // 唯一ID\n\tOpenedTables          *int    `json:\"openedTables\"`          // 开台数量\n\tSettledTables         *int    `json:\"settledTables\"`         // 已结算台数\n\tUnsettledTables       *int    `json:\"unsettledTables\"`       // 未结算台数\n\tOpenedAmount          *int64  `json:\"openedAmount\"`          // 开台金额\n\tSettledAmount         *int64  `json:\"settledAmount\"`         // 已结算金额\n\tUnsettledAmount       *int64  `json:\"unsettledAmount\"`       // 未结算金额\n\tSoldProducts          *string `json:\"soldProducts\"`          // 销售产品列表(JSON)\n\tSoldProductsAmount    *int64  `json:\"soldProductsAmount\"`    // 销售产品总金额\n\tGiftedProducts        *string `json:\"giftedProducts\"`        // 赠送产品列表(JSON)\n\tGiftedProductsAmount  *int64  `json:\"giftedProductsAmount\"`  // 赠送产品总金额\n\tReturnedProducts      *string `json:\"returnedProducts\"`      // 退货产品列表(JSON)\n\tReturnedProductsAmount *int64  `json:\"returnedProductsAmount\"` // 退货产品总金额\n\tMemberRecharge        *int64  `json:\"memberRecharge\"`        // 会员充值金额\n\tMemberConsumption     *int64  `json:\"memberConsumption\"`     // 会员消费金额\n\tPaymentMethodStats    *string `json:\"paymentMethodStats\"`    // 支付方式统计(JSON)\n\tShiftTime             *int64  `json:\"shiftTime\"`             // 班次时间\n\tPageNum               *int    `json:\"pageNum\"`               // 页码\n\tPageSize              *int    `json:\"pageSize\"`              // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype ShiftReportTransfer struct {\n}\n\nfunc (transfer *ShiftReportTransfer) PoToVo(po po.ShiftReport) vo.ShiftReportVO {\n\tvo := vo.ShiftReportVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *ShiftReportTransfer) VoToPo(vo vo.ShiftReportVO) po.ShiftReport {\n\tpo := po.ShiftReport{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype ShiftReportService struct {\n}\n\nfunc (service *ShiftReportService) CreateShiftReport(logCtx *gin.Context, shiftReport *po.ShiftReport) error {\n\treturn Save(shiftReport)\n}\n\nfunc (service *ShiftReportService) UpdateShiftReport(logCtx *gin.Context, shiftReport *po.ShiftReport) error {\n\treturn Update(shiftReport)\n}\n\nfunc (service *ShiftReportService) DeleteShiftReport(logCtx *gin.Context, id string) error {\n\treturn Delete(po.ShiftReport{Id: &id})\n}\n\nfunc (service *ShiftReportService) FindShiftReportById(logCtx *gin.Context, id string) (shiftReport *po.ShiftReport, err error) {\n\tshiftReport = &po.ShiftReport{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(shiftReport).Error\n\treturn\n}\n\nfunc (service *ShiftReportService) FindAllShiftReport(logCtx *gin.Context, reqDto *req.QueryShiftReportReqDto) (list *[]po.ShiftReport, err error) {\n\tdb := model.DBSlave.Self.Model(&po.ShiftReport{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.ShiftReport{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *ShiftReportService) FindAllShiftReportWithPagination(logCtx *gin.Context, reqDto *req.QueryShiftReportReqDto) (list *[]po.ShiftReport, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.ShiftReport{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.ShiftReport{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype ShiftReportController struct{}\n\nvar (\n\tshiftReportService  = impl.ShiftReportService{}\n\tshiftReportTransfer = transfer.ShiftReportTransfer{}\n)\n\n// @Summary 添加班次报告\n// @Description 添加班次报告\n// @Tags 班次报告\n// @Accept json\n// @Produce json\n// @Param body body req.AddShiftReportReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.ShiftReportVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/shift-report/add [post]\nfunc (controller *ShiftReportController) AddShiftReport(ctx *gin.Context) {\n\treqDto := req.AddShiftReportReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tshiftReport := po.ShiftReport{}\n\tif reqDto.OpenedTables != nil {\n\t\tshiftReport.OpenedTables = reqDto.OpenedTables\n\t}\n\tif reqDto.SettledTables != nil {\n\t\tshiftReport.SettledTables = reqDto.SettledTables\n\t}\n\tif reqDto.UnsettledTables != nil {\n\t\tshiftReport.UnsettledTables = reqDto.UnsettledTables\n\t}\n\tif reqDto.OpenedAmount != nil {\n\t\tshiftReport.OpenedAmount = reqDto.OpenedAmount\n\t}\n\tif reqDto.SettledAmount != nil {\n\t\tshiftReport.SettledAmount = reqDto.SettledAmount\n\t}\n\tif reqDto.UnsettledAmount != nil {\n\t\tshiftReport.UnsettledAmount = reqDto.UnsettledAmount\n\t}\n\tif reqDto.SoldProducts != nil {\n\t\tshiftReport.SoldProducts = reqDto.SoldProducts\n\t}\n\tif reqDto.SoldProductsAmount != nil {\n\t\tshiftReport.SoldProductsAmount = reqDto.SoldProductsAmount\n\t}\n\tif reqDto.GiftedProducts != nil {\n\t\tshiftReport.GiftedProducts = reqDto.GiftedProducts\n\t}\n\tif reqDto.GiftedProductsAmount != nil {\n\t\tshiftReport.GiftedProductsAmount = reqDto.GiftedProductsAmount\n\t}\n\tif reqDto.ReturnedProducts != nil {\n\t\tshiftReport.ReturnedProducts = reqDto.ReturnedProducts\n\t}\n\tif reqDto.ReturnedProductsAmount != nil {\n\t\tshiftReport.ReturnedProductsAmount = reqDto.ReturnedProductsAmount\n\t}\n\tif reqDto.MemberRecharge != nil {\n\t\tshiftReport.MemberRecharge = reqDto.MemberRecharge\n\t}\n\tif reqDto.MemberConsumption != nil {\n\t\tshiftReport.MemberConsumption = reqDto.MemberConsumption\n\t}\n\tif reqDto.PaymentMethodStats != nil {\n\t\tshiftReport.PaymentMethodStats = reqDto.PaymentMethodStats\n\t}\n\tif reqDto.ShiftTime != nil {\n\t\tshiftReport.ShiftTime = reqDto.ShiftTime\n\t}\n\n\terr = shiftReportService.CreateShiftReport(ctx, &shiftReport)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, shiftReportTransfer.PoToVo(shiftReport))\n}\n\n// @Summary 更新班次报告\n// @Description 更新班次报告\n// @Tags 班次报告\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateShiftReportReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.ShiftReportVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/shift-report/update [post]\nfunc (controller *ShiftReportController) UpdateShiftReport(ctx *gin.Context) {\n\treqDto := req.UpdateShiftReportReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\tshiftReport, err := shiftReportService.FindShiftReportById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tif reqDto.OpenedTables != nil {\n\t\tshiftReport.OpenedTables = reqDto.OpenedTables\n\t}\n\tif reqDto.SettledTables != nil {\n\t\tshiftReport.SettledTables = reqDto.SettledTables\n\t}\n\tif reqDto.UnsettledTables != nil {\n\t\tshiftReport.UnsettledTables = reqDto.UnsettledTables\n\t}\n\tif reqDto.OpenedAmount != nil {\n\t\tshiftReport.OpenedAmount = reqDto.OpenedAmount\n\t}\n\tif reqDto.SettledAmount != nil {\n\t\tshiftReport.SettledAmount = reqDto.SettledAmount\n\t}\n\tif reqDto.UnsettledAmount != nil {\n\t\tshiftReport.UnsettledAmount = reqDto.UnsettledAmount\n\t}\n\tif reqDto.SoldProducts != nil {\n\t\tshiftReport.SoldProducts = reqDto.SoldProducts\n\t}\n\tif reqDto.SoldProductsAmount != nil {\n\t\tshiftReport.SoldProductsAmount = reqDto.SoldProductsAmount\n\t}\n\tif reqDto.GiftedProducts != nil {\n\t\tshiftReport.GiftedProducts = reqDto.GiftedProducts\n\t}\n\tif reqDto.GiftedProductsAmount != nil {\n\t\tshiftReport.GiftedProductsAmount = reqDto.GiftedProductsAmount\n\t}\n\tif reqDto.ReturnedProducts != nil {\n\t\tshiftReport.ReturnedProducts = reqDto.ReturnedProducts\n\t}\n\tif reqDto.ReturnedProductsAmount != nil {\n\t\tshiftReport.ReturnedProductsAmount = reqDto.ReturnedProductsAmount\n\t}\n\tif reqDto.MemberRecharge != nil {\n\t\tshiftReport.MemberRecharge = reqDto.MemberRecharge\n\t}\n\tif reqDto.MemberConsumption != nil {\n\t\tshiftReport.MemberConsumption = reqDto.MemberConsumption\n\t}\n\tif reqDto.PaymentMethodStats != nil {\n\t\tshiftReport.PaymentMethodStats = reqDto.PaymentMethodStats\n\t}\n\tif reqDto.ShiftTime != nil {\n\t\tshiftReport.ShiftTime = reqDto.ShiftTime\n\t}\n\n\terr = shiftReportService.UpdateShiftReport(ctx, shiftReport)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, shiftReportTransfer.PoToVo(*shiftReport))\n}\n\n// @Summary 删除班次报告\n// @Description 删除班次报告\n// @Tags 班次报告\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteShiftReportReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/shift-report/delete [post]\nfunc (controller *ShiftReportController) DeleteShiftReport(ctx *gin.Context) {\n\treqDto := req.DeleteShiftReportReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = shiftReportService.DeleteShiftReport(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询班次报告\n// @Description 查询班次报告\n// @Tags 班次报告\n// @Accept json\n// @Produce json\n// @Param body body req.QueryShiftReportReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.ShiftReportVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/shift-report/query [post]\nfunc (controller *ShiftReportController) QueryShiftReports(ctx *gin.Context) {\n\treqDto := req.QueryShiftReportReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := shiftReportService.FindAllShiftReport(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.ShiftReportVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, shiftReportTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询班次报告列表\n// @Description 查询班次报告列表\n// @Tags 班次报告\n// @Accept json\n// @Produce json\n// @Param body body req.QueryShiftReportReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.ShiftReportVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/shift-report/list [post]\nfunc (a *ShiftReportController) ListShiftReports(ctx *gin.Context) {\n\treqDto := req.QueryShiftReportReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := shiftReportService.FindAllShiftReportWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.ShiftReportVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.ShiftReportVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, shiftReportTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype ShiftReportRoute struct {\n}\n\nfunc (s *ShiftReportRoute) InitShiftReportRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tshiftReportController := controller.ShiftReportController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/shift-report/add\", shiftReportController.AddShiftReport)    //add\n\t\troute.POST(\"/api/shift-report/update\", shiftReportController.UpdateShiftReport) //update\n\t\troute.POST(\"/api/shift-report/delete\", shiftReportController.DeleteShiftReport) //delete\n\t\troute.POST(\"/api/shift-report/query\", shiftReportController.QueryShiftReports)     //query\n\t\troute.POST(\"/api/shift-report/list\", shiftReportController.ListShiftReports)     //list\n\t}\n}\n"}]