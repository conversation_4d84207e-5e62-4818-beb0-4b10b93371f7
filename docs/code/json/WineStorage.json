[{"po": "package po\n\n// WineStorage 酒库存实体\ntype WineStorage struct {\n\tId             *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`              // ID\n\tOrderNumber    *string `gorm:\"column:order_number;type:varchar(64);default:''\" json:\"orderNumber\"`    // 订单号\n\tPhoneNumber   *string `gorm:\"column:phone_number;type:varchar(64);default:''\" json:\"phoneNumber\"`   // 电话号码\n\tHandler       *string `gorm:\"column:handler;type:varchar(64);default:''\" json:\"handler\"`           // 处理人\n\tExpirationDate *int64  `gorm:\"column:expiration_date;type:int;default:0\" json:\"expirationDate\"`    // 过期日期\n\tCtime         *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                      // 创建时间戳\n\tUtime         *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                      // 更新时间戳\n\tState         *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                      // 状态值\n\tVersion       *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`                  // 版本号\n}\n\n// TableName 设置表名\nfunc (WineStorage) TableName() string {\n\treturn \"wine_storage\"\n}\n\nfunc (w WineStorage) GetId() string {\n\treturn *w.Id\n}\n", "vo": "package vo\n\n// WineStorageVO 酒库存值对象\ntype WineStorageVO struct {\n\tId             string `json:\"id\"`             // ID\n\tOrderNumber    string `json:\"orderNumber\"`    // 订单号\n\tPhoneNumber   string `json:\"phoneNumber\"`   // 电话号码\n\tHandler       string `json:\"handler\"`       // 处理人\n\tExpirationDate int64  `json:\"expirationDate\"` // 过期日期\n\tCtime         int64  `json:\"ctime\"`         // 创建时间戳\n\tUtime         int64  `json:\"utime\"`         // 更新时间戳\n\tState         int    `json:\"state\"`         // 状态值\n\tVersion       int    `json:\"version\"`       // 版本号\n}\n", "req_add": "package req\n\n// AddWineStorageReqDto 创建酒库存请求DTO\ntype AddWineStorageReqDto struct {\n\tOrderNumber    *string `json:\"orderNumber\"`    // 订单号\n\tPhoneNumber   *string `json:\"phoneNumber\"`   // 电话号码\n\tHandler       *string `json:\"handler\"`       // 处理人\n\tExpirationDate *int64  `json:\"expirationDate\"` // 过期日期\n}\n", "req_update": "package req\n\ntype UpdateWineStorageReqDto struct {\n\tId             *string `json:\"id\"`             // ID\n\tOrderNumber    *string `json:\"orderNumber\"`    // 订单号\n\tPhoneNumber   *string `json:\"phoneNumber\"`   // 电话号码\n\tHandler       *string `json:\"handler\"`       // 处理人\n\tExpirationDate *int64  `json:\"expirationDate\"` // 过期日期\n}\n", "req_delete": "package req\n\ntype DeleteWineStorageReqDto struct {\n\tId *string `json:\"id\"` // ID\n}\n", "req_query": "package req\n\ntype QueryWineStorageReqDto struct {\n\tId             *string `json:\"id\"`             // ID\n\tOrderNumber    *string `json:\"orderNumber\"`    // 订单号\n\tPhoneNumber   *string `json:\"phoneNumber\"`   // 电话号码\n\tHandler       *string `json:\"handler\"`       // 处理人\n\tExpirationDate *int64  `json:\"expirationDate\"` // 过期日期\n\tPageNum       *int    `json:\"pageNum\"`       // 页码\n\tPageSize      *int    `json:\"pageSize\"`      // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype WineStorageTransfer struct {\n}\n\nfunc (transfer *WineStorageTransfer) PoToVo(po po.WineStorage) vo.WineStorageVO {\n\tvo := vo.WineStorageVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *WineStorageTransfer) VoToPo(vo vo.WineStorageVO) po.WineStorage {\n\tpo := po.WineStorage{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype WineStorageService struct {\n}\n\nfunc (service *WineStorageService) CreateWineStorage(logCtx *gin.Context, wineStorage *po.WineStorage) error {\n\treturn Save(wineStorage)\n}\n\nfunc (service *WineStorageService) UpdateWineStorage(logCtx *gin.Context, wineStorage *po.WineStorage) error {\n\treturn Update(wineStorage)\n}\n\nfunc (service *WineStorageService) DeleteWineStorage(logCtx *gin.Context, id string) error {\n\treturn Delete(po.WineStorage{Id: &id})\n}\n\nfunc (service *WineStorageService) FindWineStorageById(logCtx *gin.Context, id string) (wineStorage *po.WineStorage, err error) {\n\twineStorage = &po.WineStorage{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(wineStorage).Error\n\treturn\n}\n\nfunc (service *WineStorageService) FindAllWineStorage(logCtx *gin.Context, reqDto *req.QueryWineStorageReqDto) (list *[]po.WineStorage, err error) {\n\tdb := model.DBSlave.Self.Model(&po.WineStorage{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.OrderNumber != nil && *reqDto.OrderNumber != \"\" {\n\t\tdb = db.Where(\"order_number=?\", *reqDto.OrderNumber)\n\t}\n\tif reqDto.PhoneNumber != nil && *reqDto.PhoneNumber != \"\" {\n\t\tdb = db.Where(\"phone_number=?\", *reqDto.PhoneNumber)\n\t}\n\tif reqDto.Handler != nil && *reqDto.Handler != \"\" {\n\t\tdb = db.Where(\"handler=?\", *reqDto.Handler)\n\t}\n\tif reqDto.ExpirationDate != nil {\n\t\tdb = db.Where(\"expiration_date=?\", *reqDto.ExpirationDate)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.WineStorage{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *WineStorageService) FindAllWineStorageWithPagination(logCtx *gin.Context, reqDto *req.QueryWineStorageReqDto) (list *[]po.WineStorage, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.WineStorage{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.OrderNumber != nil && *reqDto.OrderNumber != \"\" {\n\t\tdb = db.Where(\"order_number=?\", *reqDto.OrderNumber)\n\t}\n\tif reqDto.PhoneNumber != nil && *reqDto.PhoneNumber != \"\" {\n\t\tdb = db.Where(\"phone_number=?\", *reqDto.PhoneNumber)\n\t}\n\tif reqDto.Handler != nil && *reqDto.Handler != \"\" {\n\t\tdb = db.Where(\"handler=?\", *reqDto.Handler)\n\t}\n\tif reqDto.ExpirationDate != nil {\n\t\tdb = db.Where(\"expiration_date=?\", *reqDto.ExpirationDate)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.WineStorage{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype WineStorageController struct{}\n\nvar (\n\twineStorageService  = impl.WineStorageService{}\n\twineStorageTransfer = transfer.WineStorageTransfer{}\n)\n\n// @Summary 添加酒库存\n// @Description 添加酒库存\n// @Tags 酒库存\n// @Accept json\n// @Produce json\n// @Param body body req.AddWineStorageReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.WineStorageVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/wine-storage/add [post]\nfunc (controller *WineStorageController) AddWineStorage(ctx *gin.Context) {\n\treqDto := req.AddWineStorageReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\twineStorage := po.WineStorage{}\n\tif reqDto.OrderNumber != nil {\n\t\twineStorage.OrderNumber = reqDto.OrderNumber\n\t}\n\tif reqDto.PhoneNumber != nil {\n\t\twineStorage.PhoneNumber = reqDto.PhoneNumber\n\t}\n\tif reqDto.Handler != nil {\n\t\twineStorage.Handler = reqDto.Handler\n\t}\n\tif reqDto.ExpirationDate != nil {\n\t\twineStorage.ExpirationDate = reqDto.ExpirationDate\n\t}\n\n\terr = wineStorageService.CreateWineStorage(ctx, &wineStorage)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, wineStorageTransfer.PoToVo(wineStorage))\n}\n\n// @Summary 更新酒库存\n// @Description 更新酒库存\n// @Tags 酒库存\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateWineStorageReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.WineStorageVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/wine-storage/update [post]\nfunc (controller *WineStorageController) UpdateWineStorage(ctx *gin.Context) {\n\treqDto := req.UpdateWineStorageReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\twineStorage, err := wineStorageService.FindWineStorageById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tif reqDto.OrderNumber != nil {\n\t\twineStorage.OrderNumber = reqDto.OrderNumber\n\t}\n\tif reqDto.PhoneNumber != nil {\n\t\twineStorage.PhoneNumber = reqDto.PhoneNumber\n\t}\n\tif reqDto.Handler != nil {\n\t\twineStorage.Handler = reqDto.Handler\n\t}\n\tif reqDto.ExpirationDate != nil {\n\t\twineStorage.ExpirationDate = reqDto.ExpirationDate\n\t}\n\n\terr = wineStorageService.UpdateWineStorage(ctx, wineStorage)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, wineStorageTransfer.PoToVo(*wineStorage))\n}\n\n// @Summary 删除酒库存\n// @Description 删除酒库存\n// @Tags 酒库存\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteWineStorageReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/wine-storage/delete [post]\nfunc (controller *WineStorageController) DeleteWineStorage(ctx *gin.Context) {\n\treqDto := req.DeleteWineStorageReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = wineStorageService.DeleteWineStorage(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询酒库存\n// @Description 查询酒库存\n// @Tags 酒库存\n// @Accept json\n// @Produce json\n// @Param body body req.QueryWineStorageReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.WineStorageVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/wine-storage/query [post]\nfunc (controller *WineStorageController) QueryWineStorages(ctx *gin.Context) {\n\treqDto := req.QueryWineStorageReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := wineStorageService.FindAllWineStorage(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.WineStorageVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, wineStorageTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询酒库存列表\n// @Description 查询酒库存列表\n// @Tags 酒库存\n// @Accept json\n// @Produce json\n// @Param body body req.QueryWineStorageReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.WineStorageVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/wine-storage/list [post]\nfunc (a *WineStorageController) ListWineStorages(ctx *gin.Context) {\n\treqDto := req.QueryWineStorageReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := wineStorageService.FindAllWineStorageWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.WineStorageVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.WineStorageVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, wineStorageTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype WineStorageRoute struct {\n}\n\nfunc (s *WineStorageRoute) InitWineStorageRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\twineStorageController := controller.WineStorageController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/wine-storage/add\", wineStorageController.AddWineStorage)    //add\n\t\troute.POST(\"/api/wine-storage/update\", wineStorageController.UpdateWineStorage) //update\n\t\troute.POST(\"/api/wine-storage/delete\", wineStorageController.DeleteWineStorage) //delete\n\t\troute.POST(\"/api/wine-storage/query\", wineStorageController.QueryWineStorages)  //query\n\t\troute.POST(\"/api/wine-storage/list\", wineStorageController.ListWineStorages)   //list\n\t}\n}\n"}]