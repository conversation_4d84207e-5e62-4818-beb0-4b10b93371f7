[{"po": "package po\n\n// Venue 门店实体\ntype Venue struct {\n\tId            *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`              // ID\n\tName          *string `gorm:\"column:name;type:varchar(255);default:''\" json:\"name\"`              // 门店名称\n\tLogo          *string `gorm:\"column:logo;type:varchar(255);default:''\" json:\"logo\"`              // 门店logo URL\n\tAddress       *string `gorm:\"column:address;type:varchar(255);default:''\" json:\"address\"`        // 门店地址\n\tBusinessHours *string `gorm:\"column:business_hours;type:varchar(255);default:''\" json:\"businessHours\"` // 营业时间\n\tPhotos        *string `gorm:\"column:photos;type:text;default:''\" json:\"photos\"`                   // 门店照片URL列表\n\tCtime         *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                      // 创建时间戳\n\tUtime         *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                      // 更新时间戳\n\tState         *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                      // 状态值\n\tVersion       *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`                  // 版本号\n}\n\n// TableName 设置表名\nfunc (Venue) TableName() string {\n\treturn \"venue\"\n}\n\nfunc (v Venue) GetId() string {\n\treturn *v.Id\n}\n", "vo": "package vo\n\n// VenueVO 门店信息值对象\ntype VenueVO struct {\n\tId            string `json:\"id\"`            // ID\n\tName          string `json:\"name\"`          // 门店名称\n\tLogo          string `json:\"logo\"`          // 门店logo URL\n\tAddress       string `json:\"address\"`       // 门店地址\n\tBusinessHours string `json:\"businessHours\"` // 营业时间\n\tPhotos        string `json:\"photos\"`        // 门店照片URL列表\n\tCtime         int64  `json:\"ctime\"`         // 创建时间戳\n\tUtime         int64  `json:\"utime\"`         // 更新时间戳\n\tState         int    `json:\"state\"`         // 状态值\n\tVersion       int    `json:\"version\"`       // 版本号\n}\n", "req_add": "package req\n\n// AddVenueReqDto 创建门店请求DTO\ntype AddVenueReqDto struct {\n\tName          *string `json:\"name\"`          // 门店名称\n\tLogo          *string `json:\"logo\"`          // 门店logo URL\n\tAddress       *string `json:\"address\"`       // 门店地址\n\tBusinessHours *string `json:\"businessHours\"` // 营业时间\n\tPhotos        *string `json:\"photos\"`        // 门店照片URL列表\n}\n", "req_update": "package req\n\n// UpdateVenueReqDto 更新门店请求DTO\ntype UpdateVenueReqDto struct {\n\tId            *string `json:\"id\"`            // ID\n\tName          *string `json:\"name\"`          // 门店名称\n\tLogo          *string `json:\"logo\"`          // 门店logo URL\n\tAddress       *string `json:\"address\"`       // 门店地址\n\tBusinessHours *string `json:\"businessHours\"` // 营业时间\n\tPhotos        *string `json:\"photos\"`        // 门店照片URL列表\n}\n", "req_delete": "package req\n\n// DeleteVenueReqDto 删除门店请求DTO\ntype DeleteVenueReqDto struct {\n\tId *string `json:\"id\"` // ID\n}\n", "req_query": "package req\n\n// QueryVenueReqDto 查询门店请求DTO\ntype QueryVenueReqDto struct {\n\tId            *string `json:\"id\"`            // ID\n\tName          *string `json:\"name\"`          // 门店名称\n\tLogo          *string `json:\"logo\"`          // 门店logo URL\n\tAddress       *string `json:\"address\"`       // 门店地址\n\tBusinessHours *string `json:\"businessHours\"` // 营业时间\n\tPhotos        *string `json:\"photos\"`        // 门店照片URL列表\n\tPageNum       *int    `json:\"pageNum\"`       // 页码\n\tPageSize      *int    `json:\"pageSize\"`      // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype VenueTransfer struct {\n}\n\nfunc (transfer *VenueTransfer) PoToVo(po po.Venue) vo.VenueVO {\n\tvo := vo.VenueVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *VenueTransfer) VoToPo(vo vo.VenueVO) po.Venue {\n\tpo := po.Venue{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype VenueService struct {\n}\n\nfunc (service *VenueService) CreateVenue(logCtx *gin.Context, venue *po.Venue) error {\n\treturn Save(venue)\n}\n\nfunc (service *VenueService) UpdateVenue(logCtx *gin.Context, venue *po.Venue) error {\n\treturn Update(venue)\n}\n\nfunc (service *VenueService) DeleteVenue(logCtx *gin.Context, id string) error {\n\treturn Delete(po.Venue{Id: &id})\n}\n\nfunc (service *VenueService) FindVenueById(logCtx *gin.Context, id string) (venue *po.Venue, err error) {\n\tvenue = &po.Venue{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(venue).Error\n\treturn\n}\n\nfunc (service *VenueService) FindAllVenue(logCtx *gin.Context, reqDto *req.QueryVenueReqDto) (list *[]po.Venue, err error) {\n\tdb := model.DBSlave.Self.Model(&po.Venue{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name LIKE ?\", \"%\"+*reqDto.Name+\"%\")\n\t}\n\tif reqDto.Address != nil && *reqDto.Address != \"\" {\n\t\tdb = db.Where(\"address LIKE ?\", \"%\"+*reqDto.Address+\"%\")\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.Venue{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *VenueService) FindAllVenueWithPagination(logCtx *gin.Context, reqDto *req.QueryVenueReqDto) (list *[]po.Venue, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.Venue{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name LIKE ?\", \"%\"+*reqDto.Name+\"%\")\n\t}\n\tif reqDto.Address != nil && *reqDto.Address != \"\" {\n\t\tdb = db.Where(\"address LIKE ?\", \"%\"+*reqDto.Address+\"%\")\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.Venue{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype VenueController struct{}\n\nvar (\n\tvenueService  = impl.VenueService{}\n\tvenueTransfer = transfer.VenueTransfer{}\n)\n\n// @Summary 添加门店\n// @Description 添加门店\n// @Tags 门店\n// @Accept json\n// @Produce json\n// @Param body body req.AddVenueReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.VenueVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/venue/add [post]\nfunc (controller *VenueController) AddVenue(ctx *gin.Context) {\n\treqDto := req.AddVenueReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tvenue := po.Venue{}\n\tif reqDto.Name != nil {\n\t\tvenue.Name = reqDto.Name\n\t}\n\tif reqDto.Logo != nil {\n\t\tvenue.Logo = reqDto.Logo\n\t}\n\tif reqDto.Address != nil {\n\t\tvenue.Address = reqDto.Address\n\t}\n\tif reqDto.BusinessHours != nil {\n\t\tvenue.BusinessHours = reqDto.BusinessHours\n\t}\n\tif reqDto.Photos != nil {\n\t\tvenue.Photos = reqDto.Photos\n\t}\n\n\terr = venueService.CreateVenue(ctx, &venue)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, venueTransfer.PoToVo(venue))\n}\n\n// @Summary 更新门店\n// @Description 更新门店\n// @Tags 门店\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateVenueReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.VenueVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/venue/update [post]\nfunc (controller *VenueController) UpdateVenue(ctx *gin.Context) {\n\treqDto := req.UpdateVenueReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\tvenue, err := venueService.FindVenueById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tif reqDto.Name != nil {\n\t\tvenue.Name = reqDto.Name\n\t}\n\tif reqDto.Logo != nil {\n\t\tvenue.Logo = reqDto.Logo\n\t}\n\tif reqDto.Address != nil {\n\t\tvenue.Address = reqDto.Address\n\t}\n\tif reqDto.BusinessHours != nil {\n\t\tvenue.BusinessHours = reqDto.BusinessHours\n\t}\n\tif reqDto.Photos != nil {\n\t\tvenue.Photos = reqDto.Photos\n\t}\n\n\terr = venueService.UpdateVenue(ctx, venue)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, venueTransfer.PoToVo(*venue))\n}\n\n// @Summary 删除门店\n// @Description 删除门店\n// @Tags 门店\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteVenueReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/venue/delete [post]\nfunc (controller *VenueController) DeleteVenue(ctx *gin.Context) {\n\treqDto := req.DeleteVenueReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = venueService.DeleteVenue(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询门店\n// @Description 查询门店\n// @Tags 门店\n// @Accept json\n// @Produce json\n// @Param body body req.QueryVenueReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.VenueVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/venue/query [post]\nfunc (controller *VenueController) QueryVenues(ctx *gin.Context) {\n\treqDto := req.QueryVenueReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := venueService.FindAllVenue(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.VenueVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, venueTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询门店列表\n// @Description 查询门店列表\n// @Tags 门店\n// @Accept json\n// @Produce json\n// @Param body body req.QueryVenueReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.VenueVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/venue/list [post]\nfunc (a *VenueController) ListVenues(ctx *gin.Context) {\n\treqDto := req.QueryVenueReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := venueService.FindAllVenueWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.VenueVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.VenueVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, venueTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype VenueRoute struct {\n}\n\nfunc (s *VenueRoute) InitVenueRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tvenueController := controller.VenueController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/venue/add\", venueController.AddVenue)       //add\n\t\troute.POST(\"/api/venue/update\", venueController.UpdateVenue) //update\n\t\troute.POST(\"/api/venue/delete\", venueController.DeleteVenue) //delete\n\t\troute.POST(\"/api/venue/query\", venueController.QueryVenues)  //query\n\t\troute.POST(\"/api/venue/list\", venueController.ListVenues)    //list\n\t}\n}\n"}]