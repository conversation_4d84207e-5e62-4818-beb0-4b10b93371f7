[{"po": "package po\n\n// SmsService 短信服务实体\ntype SmsService struct {\n\tId        *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`          // ID\n\tSignature *string `gorm:\"column:signature;type:varchar(255);default:''\" json:\"signature\"` // 签名\n\tBalance   *int    `gorm:\"column:balance;type:int;default:0\" json:\"balance\"`               // 余额\n\tCtime     *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                   // 创建时间戳\n\tUtime     *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                   // 更新时间戳\n\tState     *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                   // 状态值\n\tVersion   *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`               // 版本号\n}\n\n// TableName 设置表名\nfunc (SmsService) TableName() string {\n\treturn \"sms_service\"\n}\n\nfunc (s SmsService) GetId() string {\n\treturn *s.Id\n}\n", "vo": "package vo\n\n// SmsServiceVO 短信服务值对象\ntype SmsServiceVO struct {\n\tId        string `json:\"id\"`        // ID\n\tSignature string `json:\"signature\"` // 签名\n\tBalance   int    `json:\"balance\"`   // 余额\n\tCtime     int64  `json:\"ctime\"`     // 创建时间戳\n\tUtime     int64  `json:\"utime\"`     // 更新时间戳\n\tState     int    `json:\"state\"`     // 状态值\n\tVersion   int    `json:\"version\"`   // 版本号\n}\n", "req_add": "package req\n\n// AddSmsServiceReqDto 创建短信服务请求DTO\ntype AddSmsServiceReqDto struct {\n\tSignature *string `json:\"signature\"` // 签名\n\tBalance   *int    `json:\"balance\"`   // 余额\n}\n", "req_update": "package req\n\ntype UpdateSmsServiceReqDto struct {\n\tId        *string `json:\"id\"`        // ID\n\tSignature *string `json:\"signature\"` // 签名\n\tBalance   *int    `json:\"balance\"`   // 余额\n}\n", "req_delete": "package req\n\ntype DeleteSmsServiceReqDto struct {\n\tId *string `json:\"id\"` // ID\n}\n", "req_query": "package req\n\ntype QuerySmsServiceReqDto struct {\n\tId        *string `json:\"id\"`        // ID\n\tSignature *string `json:\"signature\"` // 签名\n\tBalance   *int    `json:\"balance\"`   // 余额\n\tPageNum   *int    `json:\"pageNum\"`   // 页码\n\tPageSize  *int    `json:\"pageSize\"`  // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype SmsServiceTransfer struct {\n}\n\nfunc (transfer *SmsServiceTransfer) PoToVo(po po.SmsService) vo.SmsServiceVO {\n\tvo := vo.SmsServiceVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *SmsServiceTransfer) VoToPo(vo vo.SmsServiceVO) po.SmsService {\n\tpo := po.SmsService{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype SmsServiceService struct {\n}\n\nfunc (service *SmsServiceService) CreateSmsService(logCtx *gin.Context, smsService *po.SmsService) error {\n\treturn Save(smsService)\n}\n\nfunc (service *SmsServiceService) UpdateSmsService(logCtx *gin.Context, smsService *po.SmsService) error {\n\treturn Update(smsService)\n}\n\nfunc (service *SmsServiceService) DeleteSmsService(logCtx *gin.Context, id string) error {\n\treturn Delete(po.SmsService{Id: &id})\n}\n\nfunc (service *SmsServiceService) FindSmsServiceById(logCtx *gin.Context, id string) (smsService *po.SmsService, err error) {\n\tsmsService = &po.SmsService{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(smsService).Error\n\treturn\n}\n\nfunc (service *SmsServiceService) FindAllSmsService(logCtx *gin.Context, reqDto *req.QuerySmsServiceReqDto) (list *[]po.SmsService, err error) {\n\tdb := model.DBSlave.Self.Model(&po.SmsService{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Signature != nil && *reqDto.Signature != \"\" {\n\t\tdb = db.Where(\"signature=?\", *reqDto.Signature)\n\t}\n\tif reqDto.Balance != nil {\n\t\tdb = db.Where(\"balance=?\", *reqDto.Balance)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.SmsService{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *SmsServiceService) FindAllSmsServiceWithPagination(logCtx *gin.Context, reqDto *req.QuerySmsServiceReqDto) (list *[]po.SmsService, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.SmsService{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Signature != nil && *reqDto.Signature != \"\" {\n\t\tdb = db.Where(\"signature=?\", *reqDto.Signature)\n\t}\n\tif reqDto.Balance != nil {\n\t\tdb = db.Where(\"balance=?\", *reqDto.Balance)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.SmsService{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype SmsServiceController struct{}\n\nvar (\n\tsmsServiceService  = impl.SmsServiceService{}\n\tsmsServiceTransfer = transfer.SmsServiceTransfer{}\n)\n\n// @Summary 添加短信服务\n// @Description 添加短信服务\n// @Tags 短信服务\n// @Accept json\n// @Produce json\n// @Param body body req.AddSmsServiceReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.SmsServiceVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/sms-service/add [post]\nfunc (controller *SmsServiceController) AddSmsService(ctx *gin.Context) {\n\treqDto := req.AddSmsServiceReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tsmsService := po.SmsService{}\n\tif reqDto.Signature != nil {\n\t\tsmsService.Signature = reqDto.Signature\n\t}\n\tif reqDto.Balance != nil {\n\t\tsmsService.Balance = reqDto.Balance\n\t}\n\terr = smsServiceService.CreateSmsService(ctx, &smsService)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, smsServiceTransfer.PoToVo(smsService))\n}\n\n// @Summary 更新短信服务\n// @Description 更新短信服务\n// @Tags 短信服务\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateSmsServiceReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.SmsServiceVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/sms-service/update [post]\nfunc (controller *SmsServiceController) UpdateSmsService(ctx *gin.Context) {\n\treqDto := req.UpdateSmsServiceReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\tsmsService, err := smsServiceService.FindSmsServiceById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Signature != nil {\n\t\tsmsService.Signature = reqDto.Signature\n\t}\n\tif reqDto.Balance != nil {\n\t\tsmsService.Balance = reqDto.Balance\n\t}\n\terr = smsServiceService.UpdateSmsService(ctx, smsService)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, smsServiceTransfer.PoToVo(*smsService))\n}\n\n// @Summary 删除短信服务\n// @Description 删除短信服务\n// @Tags 短信服务\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteSmsServiceReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/sms-service/delete [post]\nfunc (controller *SmsServiceController) DeleteSmsService(ctx *gin.Context) {\n\treqDto := req.DeleteSmsServiceReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = smsServiceService.DeleteSmsService(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询短信服务\n// @Description 查询短信服务\n// @Tags 短信服务\n// @Accept json\n// @Produce json\n// @Param body body req.QuerySmsServiceReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.SmsServiceVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/sms-service/query [post]\nfunc (controller *SmsServiceController) QuerySmsServices(ctx *gin.Context) {\n\treqDto := req.QuerySmsServiceReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := smsServiceService.FindAllSmsService(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.SmsServiceVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, smsServiceTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询短信服务列表\n// @Description 查询短信服务列表\n// @Tags 短信服务\n// @Accept json\n// @Produce json\n// @Param body body req.QuerySmsServiceReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.SmsServiceVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/sms-service/list [post]\nfunc (a *SmsServiceController) ListSmsServices(ctx *gin.Context) {\n\treqDto := req.QuerySmsServiceReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := smsServiceService.FindAllSmsServiceWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.SmsServiceVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.SmsServiceVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, smsServiceTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype SmsServiceRoute struct {\n}\n\nfunc (s *SmsServiceRoute) InitSmsServiceRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tsmsServiceController := controller.SmsServiceController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/sms-service/add\", smsServiceController.AddSmsService)       //add\n\t\troute.POST(\"/api/sms-service/update\", smsServiceController.UpdateSmsService) //update\n\t\troute.POST(\"/api/sms-service/delete\", smsServiceController.DeleteSmsService) //delete\n\t\troute.POST(\"/api/sms-service/query\", smsServiceController.QuerySmsServices)  //query\n\t\troute.POST(\"/api/sms-service/list\", smsServiceController.ListSmsServices)    //list\n\t}\n}\n"}]