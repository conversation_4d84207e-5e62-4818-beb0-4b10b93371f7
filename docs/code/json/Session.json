[{"po": "package po\n\n// Session 场次实体\ntype Session struct {\n\tId              *string  `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`              // 唯一ID\n\tSessionId       *string  `gorm:\"column:session_id;type:varchar(64);default:''\" json:\"sessionId\"`       // 开台ID\n\tVenueId         *string  `gorm:\"column:venue_id;type:varchar(64);default:''\" json:\"venueId\"`         // 门店ID\n\tRoomId          *string  `gorm:\"column:room_id;type:varchar(64);default:''\" json:\"roomId\"`          // 房间ID\n\tStartTime       *int64   `gorm:\"column:start_time;type:int;default:0\" json:\"startTime\"`       // 开台时间\n\tEndTime         *int64   `gorm:\"column:end_time;type:int;default:0\" json:\"endTime\"`         // 关房时间\n\tDuration        *int64   `gorm:\"column:duration;type:int;default:0\" json:\"duration\"`        // 使用时长\n\tStatus          *string  `gorm:\"column:status;type:varchar(64);default:''\" json:\"status\"`          // 支付状态\n\tOrderSource     *string  `gorm:\"column:order_source;type:varchar(64);default:''\" json:\"orderSource\"`     // 订单来源\n\tCustomerSource  *string  `gorm:\"column:customer_source;type:varchar(64);default:''\" json:\"customerSource\"`  // 客户来源\n\tCustomerTag     *string  `gorm:\"column:customer_tag;type:varchar(64);default:''\" json:\"customerTag\"`     // 客群标签\n\tAgentPerson     *string  `gorm:\"column:agent_person;type:varchar(64);default:''\" json:\"agentPerson\"`     // 代定人\n\tDutyPerson      *string  `gorm:\"column:duty_person;type:varchar(64);default:''\" json:\"dutyPerson\"`      // 轮房人\n\tRankNumber      *string  `gorm:\"column:rank_number;type:varchar(64);default:''\" json:\"rankNumber\"`      // 排位号码\n\tMinConsume      *int64   `gorm:\"column:min_consume;type:int;default:0\" json:\"minConsume\"`      // 最低消费\n\tRoomFee         *int64   `gorm:\"column:room_fee;type:int;default:0\" json:\"roomFee\"`         // 包厢费用\n\tSupermarketFee  *int64   `gorm:\"column:supermarket_fee;type:int;default:0\" json:\"supermarketFee\"`  // 超市费用\n\tTotalFee        *int64   `gorm:\"column:total_fee;type:int;default:0\" json:\"totalFee\"`        // 总计费用\n\tPrePayBalance   *int64   `gorm:\"column:pre_pay_balance;type:int;default:0\" json:\"prePayBalance\"`   // 预付余额\n\tIsOpenTableSettled *bool `gorm:\"column:is_open_table_settled;type:bool;default:false\" json:\"isOpenTableSettled\"` // 是否开台立结\n\tCtime           *int64   `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`           // 创建时间\n\tUtime           *int64   `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`           // 更新时间\n\tState           *int     `gorm:\"column:state;type:int;default:0\" json:\"state\"`           // 状态\n\tVersion         *int     `gorm:\"column:version;type:int;default:0\" json:\"version\"`         // 版本号\n}\n\n// TableName 设置表名\nfunc (Session) TableName() string {\n\treturn \"session\"\n}\n\nfunc (s Session) GetId() string {\n\treturn *s.Id\n}\n", "vo": "package vo\n\n// SessionVO 场次信息值对象\ntype SessionVO struct {\n\tId                string `json:\"id\"`                // 唯一ID\n\tSessionId         string `json:\"sessionId\"`         // 开台ID\n\tVenueId           string `json:\"venueId\"`           // 门店ID\n\tRoomId            string `json:\"roomId\"`            // 房间ID\n\tStartTime         int64  `json:\"startTime\"`         // 开台时间\n\tEndTime           int64  `json:\"endTime\"`           // 关房时间\n\tDuration          int64  `json:\"duration\"`          // 使用时长\n\tStatus            string `json:\"status\"`            // 支付状态\n\tOrderSource       string `json:\"orderSource\"`       // 订单来源\n\tCustomerSource    string `json:\"customerSource\"`    // 客户来源\n\tCustomerTag       string `json:\"customerTag\"`       // 客群标签\n\tAgentPerson       string `json:\"agentPerson\"`       // 代定人\n\tDutyPerson        string `json:\"dutyPerson\"`        // 轮房人\n\tRankNumber        string `json:\"rankNumber\"`        // 排位号码\n\tMinConsume        int64  `json:\"minConsume\"`        // 最低消费\n\tRoomFee           int64  `json:\"roomFee\"`           // 包厢费用\n\tSupermarketFee    int64  `json:\"supermarketFee\"`    // 超市费用\n\tTotalFee          int64  `json:\"totalFee\"`          // 总计费用\n\tPrePayBalance     int64  `json:\"prePayBalance\"`     // 预付余额\n\tIsOpenTableSettled bool   `json:\"isOpenTableSettled\"` // 是否开台立结\n\tCtime             int64  `json:\"ctime\"`             // 创建时间\n\tUtime             int64  `json:\"utime\"`             // 更新时间\n\tState             int    `json:\"state\"`             // 状态\n\tVersion           int    `json:\"version\"`           // 版本号\n}\n", "req_add": "package req\n\n// AddSessionReqDto 创建场次请求DTO\ntype AddSessionReqDto struct {\n\tSessionId         *string `json:\"sessionId\"`         // 开台ID\n\tVenueId           *string `json:\"venueId\"`           // 门店ID\n\tRoomId            *string `json:\"roomId\"`            // 房间ID\n\tStartTime         *int64  `json:\"startTime\"`         // 开台时间\n\tEndTime           *int64  `json:\"endTime\"`           // 关房时间\n\tDuration          *int64  `json:\"duration\"`          // 使用时长\n\tStatus            *string `json:\"status\"`            // 支付状态\n\tOrderSource       *string `json:\"orderSource\"`       // 订单来源\n\tCustomerSource    *string `json:\"customerSource\"`    // 客户来源\n\tCustomerTag       *string `json:\"customerTag\"`       // 客群标签\n\tAgentPerson       *string `json:\"agentPerson\"`       // 代定人\n\tDutyPerson        *string `json:\"dutyPerson\"`        // 轮房人\n\tRankNumber        *string `json:\"rankNumber\"`        // 排位号码\n\tMinConsume        *int64  `json:\"minConsume\"`        // 最低消费\n\tRoomFee           *int64  `json:\"roomFee\"`           // 包厢费用\n\tSupermarketFee    *int64  `json:\"supermarketFee\"`    // 超市费用\n\tTotalFee          *int64  `json:\"totalFee\"`          // 总计费用\n\tPrePayBalance     *int64  `json:\"prePayBalance\"`     // 预付余额\n\tIsOpenTableSettled *bool   `json:\"isOpenTableSettled\"` // 是否开台立结\n}\n", "req_update": "package req\n\n// UpdateSessionReqDto 更新场次请求DTO\ntype UpdateSessionReqDto struct {\n\tId                *string `json:\"id\"`                // 唯一ID\n\tSessionId         *string `json:\"sessionId\"`         // 开台ID\n\tVenueId           *string `json:\"venueId\"`           // 门店ID\n\tRoomId            *string `json:\"roomId\"`            // 房间ID\n\tStartTime         *int64  `json:\"startTime\"`         // 开台时间\n\tEndTime           *int64  `json:\"endTime\"`           // 关房时间\n\tDuration          *int64  `json:\"duration\"`          // 使用时长\n\tStatus            *string `json:\"status\"`            // 支付状态\n\tOrderSource       *string `json:\"orderSource\"`       // 订单来源\n\tCustomerSource    *string `json:\"customerSource\"`    // 客户来源\n\tCustomerTag       *string `json:\"customerTag\"`       // 客群标签\n\tAgentPerson       *string `json:\"agentPerson\"`       // 代定人\n\tDutyPerson        *string `json:\"dutyPerson\"`        // 轮房人\n\tRankNumber        *string `json:\"rankNumber\"`        // 排位号码\n\tMinConsume        *int64  `json:\"minConsume\"`        // 最低消费\n\tRoomFee           *int64  `json:\"roomFee\"`           // 包厢费用\n\tSupermarketFee    *int64  `json:\"supermarketFee\"`    // 超市费用\n\tTotalFee          *int64  `json:\"totalFee\"`          // 总计费用\n\tPrePayBalance     *int64  `json:\"prePayBalance\"`     // 预付余额\n\tIsOpenTableSettled *bool   `json:\"isOpenTableSettled\"` // 是否开台立结\n}\n", "req_delete": "package req\n\ntype DeleteSessionReqDto struct {\n\tId *string `json:\"id\"` // 唯一ID\n}\n", "req_query": "package req\n\ntype QuerySessionReqDto struct {\n\tId                *string `json:\"id\"`                // 唯一ID\n\tSessionId         *string `json:\"sessionId\"`         // 开台ID\n\tVenueId           *string `json:\"venueId\"`           // 门店ID\n\tRoomId            *string `json:\"roomId\"`            // 房间ID\n\tStartTime         *int64  `json:\"startTime\"`         // 开台时间\n\tEndTime           *int64  `json:\"endTime\"`           // 关房时间\n\tStatus            *string `json:\"status\"`            // 支付状态\n\tOrderSource       *string `json:\"orderSource\"`       // 订单来源\n\tCustomerSource    *string `json:\"customerSource\"`    // 客户来源\n\tCustomerTag       *string `json:\"customerTag\"`       // 客群标签\n\tAgentPerson       *string `json:\"agentPerson\"`       // 代定人\n\tDutyPerson        *string `json:\"dutyPerson\"`        // 轮房人\n\tRankNumber        *string `json:\"rankNumber\"`        // 排位号码\n\tIsOpenTableSettled *bool   `json:\"isOpenTableSettled\"` // 是否开台立结\n\tPageNum           *int    `json:\"pageNum\"`           // 页码\n\tPageSize          *int    `json:\"pageSize\"`          // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype SessionTransfer struct {\n}\n\nfunc (transfer *SessionTransfer) PoToVo(po po.Session) vo.SessionVO {\n\tvo := vo.SessionVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *SessionTransfer) VoToPo(vo vo.SessionVO) po.Session {\n\tpo := po.Session{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype SessionService struct {\n}\n\nfunc (service *SessionService) CreateSession(logCtx *gin.Context, session *po.Session) error {\n\treturn Save(session)\n}\n\nfunc (service *SessionService) UpdateSession(logCtx *gin.Context, session *po.Session) error {\n\treturn Update(session)\n}\n\nfunc (service *SessionService) DeleteSession(logCtx *gin.Context, id string) error {\n\treturn Delete(po.Session{Id: &id})\n}\n\nfunc (service *SessionService) FindSessionById(logCtx *gin.Context, id string) (session *po.Session, err error) {\n\tsession = &po.Session{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(session).Error\n\treturn\n}\n\nfunc (service *SessionService) FindAllSession(logCtx *gin.Context, reqDto *req.QuerySessionReqDto) (list *[]po.Session, err error) {\n\tdb := model.DBSlave.Self.Model(&po.Session{})\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.SessionId != nil && *reqDto.SessionId != \"\" {\n\t\tdb = db.Where(\"session_id=?\", *reqDto.SessionId)\n\t}\n\tif reqDto.VenueId != nil && *reqDto.VenueId != \"\" {\n\t\tdb = db.Where(\"venue_id=?\", *reqDto.VenueId)\n\t}\n\tif reqDto.RoomId != nil && *reqDto.RoomId != \"\" {\n\t\tdb = db.Where(\"room_id=?\", *reqDto.RoomId)\n\t}\n\tif reqDto.StartTime != nil {\n\t\tdb = db.Where(\"start_time=?\", *reqDto.StartTime)\n\t}\n\tif reqDto.EndTime != nil {\n\t\tdb = db.Where(\"end_time=?\", *reqDto.EndTime)\n\t}\n\tif reqDto.Status != nil && *reqDto.Status != \"\" {\n\t\tdb = db.Where(\"status=?\", *reqDto.Status)\n\t}\n\tif reqDto.OrderSource != nil && *reqDto.OrderSource != \"\" {\n\t\tdb = db.Where(\"order_source=?\", *reqDto.OrderSource)\n\t}\n\tif reqDto.CustomerSource != nil && *reqDto.CustomerSource != \"\" {\n\t\tdb = db.Where(\"customer_source=?\", *reqDto.CustomerSource)\n\t}\n\tif reqDto.CustomerTag != nil && *reqDto.CustomerTag != \"\" {\n\t\tdb = db.Where(\"customer_tag=?\", *reqDto.CustomerTag)\n\t}\n\tif reqDto.AgentPerson != nil && *reqDto.AgentPerson != \"\" {\n\t\tdb = db.Where(\"agent_person=?\", *reqDto.AgentPerson)\n\t}\n\tif reqDto.DutyPerson != nil && *reqDto.DutyPerson != \"\" {\n\t\tdb = db.Where(\"duty_person=?\", *reqDto.DutyPerson)\n\t}\n\tif reqDto.RankNumber != nil && *reqDto.RankNumber != \"\" {\n\t\tdb = db.Where(\"rank_number=?\", *reqDto.RankNumber)\n\t}\n\tif reqDto.IsOpenTableSettled != nil {\n\t\tdb = db.Where(\"is_open_table_settled=?\", *reqDto.IsOpenTableSettled)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.Session{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *SessionService) FindAllSessionWithPagination(logCtx *gin.Context, reqDto *req.QuerySessionReqDto) (list *[]po.Session, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.Session{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.SessionId != nil && *reqDto.SessionId != \"\" {\n\t\tdb = db.Where(\"session_id=?\", *reqDto.SessionId)\n\t}\n\tif reqDto.VenueId != nil && *reqDto.VenueId != \"\" {\n\t\tdb = db.Where(\"venue_id=?\", *reqDto.VenueId)\n\t}\n\tif reqDto.RoomId != nil && *reqDto.RoomId != \"\" {\n\t\tdb = db.Where(\"room_id=?\", *reqDto.RoomId)\n\t}\n\tif reqDto.StartTime != nil {\n\t\tdb = db.Where(\"start_time=?\", *reqDto.StartTime)\n\t}\n\tif reqDto.EndTime != nil {\n\t\tdb = db.Where(\"end_time=?\", *reqDto.EndTime)\n\t}\n\tif reqDto.Status != nil && *reqDto.Status != \"\" {\n\t\tdb = db.Where(\"status=?\", *reqDto.Status)\n\t}\n\tif reqDto.OrderSource != nil && *reqDto.OrderSource != \"\" {\n\t\tdb = db.Where(\"order_source=?\", *reqDto.OrderSource)\n\t}\n\tif reqDto.CustomerSource != nil && *reqDto.CustomerSource != \"\" {\n\t\tdb = db.Where(\"customer_source=?\", *reqDto.CustomerSource)\n\t}\n\tif reqDto.CustomerTag != nil && *reqDto.CustomerTag != \"\" {\n\t\tdb = db.Where(\"customer_tag=?\", *reqDto.CustomerTag)\n\t}\n\tif reqDto.AgentPerson != nil && *reqDto.AgentPerson != \"\" {\n\t\tdb = db.Where(\"agent_person=?\", *reqDto.AgentPerson)\n\t}\n\tif reqDto.DutyPerson != nil && *reqDto.DutyPerson != \"\" {\n\t\tdb = db.Where(\"duty_person=?\", *reqDto.DutyPerson)\n\t}\n\tif reqDto.RankNumber != nil && *reqDto.RankNumber != \"\" {\n\t\tdb = db.Where(\"rank_number=?\", *reqDto.RankNumber)\n\t}\n\tif reqDto.IsOpenTableSettled != nil {\n\t\tdb = db.Where(\"is_open_table_settled=?\", *reqDto.IsOpenTableSettled)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.Session{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype SessionController struct{}\n\nvar (\n\tsessionService  = impl.SessionService{}\n\tsessionTransfer = transfer.SessionTransfer{}\n)\n\n// @Summary 添加场次\n// @Description 添加场次\n// @Tags 场次\n// @Accept json\n// @Produce json\n// @Param body body req.AddSessionReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.SessionVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/session/add [post]\nfunc (controller *SessionController) AddSession(ctx *gin.Context) {\n\treqDto := req.AddSessionReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tsession := po.Session{}\n\tif reqDto.SessionId != nil {\n\t\tsession.SessionId = reqDto.SessionId\n\t}\n\tif reqDto.VenueId != nil {\n\t\tsession.VenueId = reqDto.VenueId\n\t}\n\tif reqDto.RoomId != nil {\n\t\tsession.RoomId = reqDto.RoomId\n\t}\n\tif reqDto.StartTime != nil {\n\t\tsession.StartTime = reqDto.StartTime\n\t}\n\tif reqDto.EndTime != nil {\n\t\tsession.EndTime = reqDto.EndTime\n\t}\n\tif reqDto.Duration != nil {\n\t\tsession.Duration = reqDto.Duration\n\t}\n\tif reqDto.Status != nil {\n\t\tsession.Status = reqDto.Status\n\t}\n\tif reqDto.OrderSource != nil {\n\t\tsession.OrderSource = reqDto.OrderSource\n\t}\n\tif reqDto.CustomerSource != nil {\n\t\tsession.CustomerSource = reqDto.CustomerSource\n\t}\n\tif reqDto.CustomerTag != nil {\n\t\tsession.CustomerTag = reqDto.CustomerTag\n\t}\n\tif reqDto.AgentPerson != nil {\n\t\tsession.AgentPerson = reqDto.AgentPerson\n\t}\n\tif reqDto.DutyPerson != nil {\n\t\tsession.DutyPerson = reqDto.DutyPerson\n\t}\n\tif reqDto.RankNumber != nil {\n\t\tsession.RankNumber = reqDto.RankNumber\n\t}\n\tif reqDto.MinConsume != nil {\n\t\tsession.MinConsume = reqDto.MinConsume\n\t}\n\tif reqDto.RoomFee != nil {\n\t\tsession.RoomFee = reqDto.RoomFee\n\t}\n\tif reqDto.SupermarketFee != nil {\n\t\tsession.SupermarketFee = reqDto.SupermarketFee\n\t}\n\tif reqDto.TotalFee != nil {\n\t\tsession.TotalFee = reqDto.TotalFee\n\t}\n\tif reqDto.PrePayBalance != nil {\n\t\tsession.PrePayBalance = reqDto.PrePayBalance\n\t}\n\tif reqDto.IsOpenTableSettled != nil {\n\t\tsession.IsOpenTableSettled = reqDto.IsOpenTableSettled\n\t}\n\n\terr = sessionService.CreateSession(ctx, &session)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, sessionTransfer.PoToVo(session))\n}\n\n// @Summary 更新场次\n// @Description 更新场次\n// @Tags 场次\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateSessionReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.SessionVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/session/update [post]\nfunc (controller *SessionController) UpdateSession(ctx *gin.Context) {\n\treqDto := req.UpdateSessionReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\tsession, err := sessionService.FindSessionById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tif reqDto.SessionId != nil {\n\t\tsession.SessionId = reqDto.SessionId\n\t}\n\tif reqDto.VenueId != nil {\n\t\tsession.VenueId = reqDto.VenueId\n\t}\n\tif reqDto.RoomId != nil {\n\t\tsession.RoomId = reqDto.RoomId\n\t}\n\tif reqDto.StartTime != nil {\n\t\tsession.StartTime = reqDto.StartTime\n\t}\n\tif reqDto.EndTime != nil {\n\t\tsession.EndTime = reqDto.EndTime\n\t}\n\tif reqDto.Duration != nil {\n\t\tsession.Duration = reqDto.Duration\n\t}\n\tif reqDto.Status != nil {\n\t\tsession.Status = reqDto.Status\n\t}\n\tif reqDto.OrderSource != nil {\n\t\tsession.OrderSource = reqDto.OrderSource\n\t}\n\tif reqDto.CustomerSource != nil {\n\t\tsession.CustomerSource = reqDto.CustomerSource\n\t}\n\tif reqDto.CustomerTag != nil {\n\t\tsession.CustomerTag = reqDto.CustomerTag\n\t}\n\tif reqDto.AgentPerson != nil {\n\t\tsession.AgentPerson = reqDto.AgentPerson\n\t}\n\tif reqDto.DutyPerson != nil {\n\t\tsession.DutyPerson = reqDto.DutyPerson\n\t}\n\tif reqDto.RankNumber != nil {\n\t\tsession.RankNumber = reqDto.RankNumber\n\t}\n\tif reqDto.MinConsume != nil {\n\t\tsession.MinConsume = reqDto.MinConsume\n\t}\n\tif reqDto.RoomFee != nil {\n\t\tsession.RoomFee = reqDto.RoomFee\n\t}\n\tif reqDto.SupermarketFee != nil {\n\t\tsession.SupermarketFee = reqDto.SupermarketFee\n\t}\n\tif reqDto.TotalFee != nil {\n\t\tsession.TotalFee = reqDto.TotalFee\n\t}\n\tif reqDto.PrePayBalance != nil {\n\t\tsession.PrePayBalance = reqDto.PrePayBalance\n\t}\n\tif reqDto.IsOpenTableSettled != nil {\n\t\tsession.IsOpenTableSettled = reqDto.IsOpenTableSettled\n\t}\n\n\terr = sessionService.UpdateSession(ctx, session)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, sessionTransfer.PoToVo(*session))\n}\n\n// @Summary 删除场次\n// @Description 删除场次\n// @Tags 场次\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteSessionReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/session/delete [post]\nfunc (controller *SessionController) DeleteSession(ctx *gin.Context) {\n\treqDto := req.DeleteSessionReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = sessionService.DeleteSession(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询场次\n// @Description 查询场次\n// @Tags 场次\n// @Accept json\n// @Produce json\n// @Param body body req.QuerySessionReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.SessionVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/session/query [post]\nfunc (controller *SessionController) QuerySessions(ctx *gin.Context) {\n\treqDto := req.QuerySessionReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := sessionService.FindAllSession(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.SessionVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, sessionTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询场次列表\n// @Description 查询场次列表\n// @Tags 场次\n// @Accept json\n// @Produce json\n// @Param body body req.QuerySessionReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.SessionVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/session/list [post]\nfunc (a *SessionController) ListSessions(ctx *gin.Context) {\n\treqDto := req.QuerySessionReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := sessionService.FindAllSessionWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.SessionVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.SessionVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, sessionTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype SessionRoute struct {\n}\n\nfunc (s *SessionRoute) InitSessionRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tsessionController := controller.SessionController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/session/add\", sessionController.AddSession)    //add\n\t\troute.POST(\"/api/session/update\", sessionController.UpdateSession) //update\n\t\troute.POST(\"/api/session/delete\", sessionController.DeleteSession) //delete\n\t\troute.POST(\"/api/session/query\", sessionController.QuerySessions)     //query\n\t\troute.POST(\"/api/session/list\", sessionController.ListSessions)     //list\n\t}\n}\n"}]