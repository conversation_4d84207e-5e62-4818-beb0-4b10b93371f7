# 存取酒管理API文档

本文档描述KTV ERP系统中存取酒相关的API接口及其调用关系。

## 1. 接口概览

### 1.1 存酒相关接口

| 接口路径 | 方法 | 功能描述 | 前端使用场景 |
| --- | --- | --- | --- |
| `/api/product-storage/add` | POST | 创建存酒记录（智能判断：单个商品直接创建记录，多个商品则创建存酒单及其明细） | 存酒页面提交表单时调用 |
| `/api/product-storage/query` | POST | 查询存酒记录（支持多种筛选条件，返回存酒明细列表） | 存酒列表页面加载数据时调用 |
| `/api/product-storage/detail/{id}` | GET | 获取存酒明细详情（单个商品的存酒记录详情，包含操作历史） | 点击存酒列表中的"查看"按钮时调用 |
| `/api/product-storage/order/{orderNo}` | GET | 获取完整存酒单信息（包括主单信息及所有关联商品） | 存酒单详情页面加载数据时调用 |
| `/api/product-storage/operate` | POST | 存酒记录操作（支持续存、报废、向已有存酒单添加商品、更新存酒记录等操作） | 操作存酒记录时调用（续存/报废/添加商品等） |
| `/api/product-storage/statistics` | POST | 获取存酒统计信息（总量、剩余量等统计数据） | 存酒统计看板加载数据时调用 |

### 1.2 取酒相关接口

| 接口路径 | 方法 | 功能描述 | 前端使用场景 |
| --- | --- | --- | --- |
| `/api/product-withdraw/add` | POST | 创建取酒记录（单个商品取酒，自动更新剩余量） | 取酒页面提交单个商品取酒表单时调用 |
| `/api/product-withdraw/batch-add` | POST | 批量创建取酒记录（支持一次取多个商品） | 取酒页面提交多个商品取酒表单时调用 |
| `/api/product-withdraw/query` | POST | 查询取酒记录（支持多种筛选条件，返回取酒记录列表） | 取酒记录列表页面加载数据时调用 |

## 2. 数据模型与关系

### 2.1 核心数据模型

系统采用了"存酒单-存酒商品"和"取酒单-取酒商品"的两层结构模型，主要包括以下四个核心实体：

1. **存酒单 (ProductStorageOrder)** - 记录一次存酒操作的主单
2. **存酒商品 (ProductStorage)** - 记录每个存放商品的明细
3. **取酒单 (ProductWithdrawOrder)** - 记录一次取酒操作的主单
4. **取酒商品 (ProductWithdraw)** - 记录每个取用商品的明细

### 2.2 实体关系图

```
┌─────────────────────────┐       ┌─────────────────────────┐
│  ProductStorageOrder    │       │    ProductWithdrawOrder │
│ (存酒单主表)            │       │    (取酒单主表)         │
├─────────────────────────┤       ├─────────────────────────┤
│ id: 主键                │       │ id: 主键                │
│ order_no: 存酒单号(PS*)│◄──┐   │ order_no: 取酒单号(PW*)│◄──┐
│ venue_id: 门店ID        │   │   │ venue_id: 门店ID        │   │
│ customer_id: 客户ID     │   │   │ customer_id: 客户ID     │   │
│ total_items: 商品总项数 │   │   │ total_items: 商品总项数 │   │
│ total_quantity: 总数量  │   │   │ total_quantity: 总数量  │   │
│ remaining_quantity: 剩余│   │   │ withdraw_time: 取酒时间 │   │
└─────────────────────────┘   │   └─────────────────────────┘   │
                              │                                 │
                              │                                 │
                              │   ┌───────────────────────────┐ │
┌─────────────────────────┐   │   │    ProductWithdraw       │ │
│    ProductStorage       │   │   │    (取酒明细表)          │ │
│    (存酒明细表)         │   │   ├───────────────────────────┤ │
├─────────────────────────┤   │   │ id: 主键                 │ │
│ id: 主键                │   │   │ order_no: 明细单号       │ │
│ order_no: 明细单号      │   │   │ parent_order_no: 取酒单号├─┘
│ parent_order_no: 存酒单号├───┘   │ storage_id: 存酒明细ID  │──┐
│ product_id: 商品ID      │       │ storage_order_no: 存酒单号│  │
│ product_name: 商品名称  │       │ product_id: 商品ID       │  │
│ quantity: 存入数量      │       │ quantity: 取出数量       │  │
│ remaining_qty: 剩余数量 │◄──────┘ withdraw_time: 取酒时间  │  │
└─────────────────────────┘       └───────────────────────────┘  │
        ▲                                                        │
        └────────────────────────────────────────────────────────┘
```

### 2.3 核心字段说明

#### 2.3.1 存酒单 (ProductStorageOrder)

存酒单是存酒的主要单据，记录一次存酒操作的整体信息，包含以下主要字段：
- `id`: 系统生成的唯一标识符
- `order_no`: 存酒单号，格式为"PS" + 门店ID + 时间戳，例如"PS94YTNnVUk1744595036"
- `venue_id`: 门店ID
- `customer_id`: 客户ID，通常格式为"C_" + 手机后四位 + 随机字符
- `customer_name`: 客户姓名
- `phone_number`: 客户电话号码
- `storage_time`: 存入时间（Unix时间戳）
- `total_items`: 商品总项数（存了多少种商品）
- `total_quantity`: 商品总数量（存了多少件商品）
- `remaining_quantity`: 剩余总数量（还剩多少件商品）
- `handler`: 处理人
- `remark`: 备注信息
- 系统字段：`ctime`, `utime`, `state`, `version`

#### 2.3.2 存酒明细 (ProductStorage)

存酒明细记录具体的单项存酒信息，一个存酒单可以包含多个存酒明细（多种商品）：
- `id`: 系统生成的唯一标识符
- `order_no`: 明细单号，格式为存酒单号 + "-" + 序号，例如"PS94YTNnVUk1744595036-001"
- `parent_order_no`: 父订单号，关联到主存酒单的order_no
- `venue_id`: 门店ID
- `customer_id`/`customer_name`/`phone_number`: 客户信息
- `product_id`/`product_name`: 商品信息
- `product_type`: 商品类型（如"酒水"、"零食"、"饮料"等）
- `product_unit`: 商品单位（如"瓶"、"杯"）
- `product_spec`: 商品规格（如"500ml"、"整瓶"）
- `quantity`: 存入数量
- `remaining_qty`: 剩余数量
- `storage_location`: 存放位置（main:仓库  wine: 存酒仓库）
- `storage_time`: 存入时间（Unix时间戳）
- `expire_time`: 到期时间（Unix时间戳）
- `is_batch`/`batch_time`: 批量操作相关信息
- 系统字段：`ctime`, `utime`, `state`, `version`

#### 2.3.3 取酒单与取酒明细

取酒相关结构与存酒类似，采用主单-明细的结构设计。其中：
- 取酒单号格式为"PW" + 门店ID + 时间戳
- 取酒明细通过`storage_id`关联到具体的存酒记录
- 每次取酒会自动更新对应存酒记录的`remaining_qty`

### 2.4 重要关系说明

1. **一对多关系**:
   - 一个存酒单(`ProductStorageOrder`)对应多个存酒明细(`ProductStorage`)
   - 一个取酒单(`ProductWithdrawOrder`)对应多个取酒明细(`ProductWithdraw`)

2. **关联关系**:
   - 存酒明细通过`parent_order_no`关联到存酒单
   - 取酒明细通过`parent_order_no`关联到取酒单
   - 取酒明细通过`storage_id`关联到具体的存酒明细记录

### 2.5 订单编号规则详解

#### 2.5.1 存酒单号（OrderNo）规则

存酒单号是系统中唯一标识一个存酒单据的编码，具有以下特点：

1. **格式构成**：
   - 前缀：`PS`（Product Storage 的缩写）
   - 门店ID：例如`94YTNnVUk`（门店的唯一标识符）
   - 时间戳：Unix 时间戳（秒级），例如`1744595036`
   - 完整示例：`PS94YTNnVUk1744595036`

2. **生成时机**：
   - 当创建新的存酒单时自动生成
   - 批量存入多个商品时，生成一个主存酒单号

3. **使用场景**：
   - 作为存酒主单的唯一标识
   - 用于关联查询所有属于同一批次存入的商品
   - 在取酒时可以通过存酒单号快速找到客户的所有存酒记录

#### 2.5.2 存酒商品单号规则

存酒商品单号是标识具体某个存酒商品的编码，具有以下特点：

1. **格式构成**：
   - 基础部分：继承存酒单号，例如`PS94YTNnVUk1744595036`
   - 分隔符：`-`
   - 序号：三位数字，从001开始递增，例如`001`、`002`
   - 完整示例：`PS94YTNnVUk1744595036-001`

2. **生成规则**：
   - 单个商品存酒：生成形如`PS门店ID时间戳-001`的编号
   - 多个商品批量存酒：系统为每个商品项生成递增序号，如`-001`、`-002`、`-003`

3. **使用场景**：
   - 作为存酒明细的唯一标识
   - 取酒操作时需指定具体的存酒商品单号
   - 通过前缀部分可以关联到对应的存酒单主记录

#### 2.5.3 取酒单号规则

取酒单号是系统中唯一标识一个取酒单据的编码：

1. **格式构成**：
   - 前缀：`PW`（Product Withdraw 的缩写）
   - 门店ID：例如`94YTNnVUk`
   - 时间戳：Unix 时间戳（秒级）
   - 完整示例：`PW94YTNnVUk1744596240`

2. **生成规则**：
   - 当创建新的取酒记录时自动生成
   - 批量取多个商品时，生成一个主取酒单号

#### 2.5.4 取酒商品单号规则

类似存酒商品单号，采用主单号加序号的形式：

1. **格式构成**：
   - 基础部分：取酒单号
   - 分隔符：`-`
   - 序号：三位数字，从001开始递增
   - 完整示例：`PW94YTNnVUk1744596240-001`

#### 2.5.5 编号使用注意事项

1. **单号查询**：
   - 存酒单号可用于查询整个存酒单及其所有明细（`/api/product-storage/order/{orderNo}`）
   - 存酒商品单号用于查询单个商品的存酒明细（`/api/product-storage/detail/{id}`）

2. **关联关系**：
   - 存酒明细中的`parent_order_no`字段存储对应的存酒单号
   - 取酒明细中的`storage_order_no`字段存储关联的存酒商品单号

3. **业务流程中的应用**：
   - 前端展示订单时，可通过单号前缀（PS/PW）区分存酒单和取酒单
   - 列表查询时可使用单号作为筛选条件
   - 单号中包含的时间戳可用于排序和业务统计

4. **单号规范**：
   - 所有单号均为系统自动生成，不允许手动修改
   - 单号全局唯一，可用作数据同步和溯源的关键标识

## 3. 前端调用场景与功能

### 3.1 存酒功能

#### 3.1.1 存酒列表页面

**功能描述**：
- 显示所有存酒记录
- 支持按客户信息、商品名称等搜索
- 支持按状态筛选（已存、部分取出、已取完）
- 每条记录显示存酒商品的基本信息和状态

**调用接口**：
- `/api/product-storage/query` - 获取存酒记录列表

**示例实现**：
```javascript
// 获取存酒列表
async function fetchStorageList(params) {
  try {
    const response = await axios.post('/api/product-storage/query', {
      venueId: currentVenueId,
      pageNum: params.pageNum || 1,
      pageSize: params.pageSize || 10,
      searchText: params.searchText,
      onlyRemaining: params.onlyRemaining || false
    });
    
    // 处理返回结果
    if (response.data.code === 0) {
      return {
        total: response.data.data.total,
        list: response.data.data.list.map(item => ({
          ...item,
          // 转换时间戳为可读时间
          storageTimeFormatted: formatTimestamp(item.storageTime),
          expireTimeFormatted: formatTimestamp(item.expireTime),
          // 计算状态：全部剩余=已存，部分剩余=部分支取，无剩余=已取完
          status: item.remainingQty === 0 ? '已取完' : 
                 item.remainingQty === item.quantity ? '已存' : '部分支取'
        }))
      };
    }
    return { total: 0, list: [] };
  } catch (error) {
    console.error('获取存酒列表失败', error);
    return { total: 0, list: [] };
  }
}
```

#### 3.1.2 存酒详情页面

**功能描述**：
- 显示单个存酒商品的详细信息
- 显示该商品的存取操作历史记录
- 提供续存、报废等操作入口

**调用接口**：
- `/api/product-storage/detail/{id}` - 获取存酒明细详情及操作历史

**示例实现**：
```javascript
// 获取存酒详情和操作历史
async function fetchStorageDetail(id) {
  try {
    const response = await axios.get(`/api/product-storage/detail/${id}`);
    
    if (response.data.code === 0) {
      const detail = response.data.data;
      
      // 处理展示数据
      return {
        basicInfo: {
          orderNo: detail.orderNo,
          customerName: detail.customerName,
          phoneNumber: detail.phoneNumber,
          productName: detail.productName,
          quantity: detail.quantity,
          remainingQty: detail.remainingQty,
          storageLocation: detail.storageLocation,
          storageTime: formatTimestamp(detail.storageTime),
          expireTime: formatTimestamp(detail.expireTime)
        },
        // 操作历史记录
        operationHistory: detail.operationHistory.map(op => ({
          type: op.type, // 存酒/取酒/续存/报废
          time: formatTimestamp(op.time),
          quantity: op.quantity,
          handler: op.handler,
          balanceQty: op.balanceQty
        }))
      };
    }
    return null;
  } catch (error) {
    console.error('获取存酒详情失败', error);
    return null;
  }
}
```

#### 3.1.3 创建存酒记录

**功能描述**：
- 支持单个商品存酒
- 支持多个商品批量存酒（创建存酒单）
- 自动生成存酒单号和关联明细

**调用接口**：
- `/api/product-storage/add` - 创建存酒记录

**单个商品存酒示例**：
```javascript
// 单个商品存酒
async function addSingleStorage(formData) {
  try {
    const payload = {
      venueId: currentVenueId,
      customerId: formData.customerId || '',
      customerName: formData.customerName,
      phoneNumber: formData.phoneNumber,
      productId: formData.productId,
      productName: formData.productName,
      productUnit: formData.productUnit,
      productSpec: formData.productSpec,
      quantity: formData.quantity,
      storageLocation: formData.storageLocation,
      storageTime: Math.floor(new Date().getTime() / 1000), // 当前时间戳
      expireTime: Math.floor(new Date(formData.expireDate).getTime() / 1000), // 到期时间戳
      remark: formData.remark,
      handler: currentUser.id
    };
    
    const response = await axios.post('/api/product-storage/add', payload);
    return response.data.code === 0;
  } catch (error) {
    console.error('创建存酒记录失败', error);
    return false;
  }
}
```

**多个商品存酒示例**：
```javascript
// 多个商品批量存酒
async function addBatchStorage(formData) {
  try {
    const payload = {
      venueId: currentVenueId,
      customerId: formData.customerId || '',
      customerName: formData.customerName,
      phoneNumber: formData.phoneNumber,
      storageTime: Math.floor(new Date().getTime() / 1000),
      remark: formData.remark,
      handler: currentUser.id,
      // 多个商品项
      items: formData.products.map(product => ({
        productId: product.id,
        productName: product.name,
        productUnit: product.unit,
        productSpec: product.spec,
        quantity: product.quantity,
        storageLocation: product.location,
        expireTime: Math.floor(new Date(product.expireDate).getTime() / 1000),
        remark: product.remark
      }))
    };
    
    const response = await axios.post('/api/product-storage/add', payload);
    return response.data.code === 0;
  } catch (error) {
    console.error('批量创建存酒记录失败', error);
    return false;
  }
}
```

### 3.2 取酒功能

#### 3.2.1 取酒流程

1. 客户需要取用之前存放的酒水
2. 前端通过存酒列表或客户信息查询该客户的存酒记录
3. 选择要取出的商品及数量，提交取酒请求
4. 后端系统检查数量是否合法，并创建取酒记录
5. 同时更新存酒记录的剩余数量
6. 返回结果给前端，前端更新界面显示取酒成功和剩余数量

#### 3.2.2 存酒单详情页查看

**功能描述**：
- 查看完整存酒单及其下所有商品明细
- 显示每个商品的存取状态

**调用接口**：
- `/api/product-storage/order/{orderNo}` - 获取完整存酒单信息

**示例实现**：
```javascript
// 获取存酒单详情
async function fetchOrderDetail(orderNo) {
  try {
    const response = await axios.get(`/api/product-storage/order/${orderNo}`);
    
    if (response.data.code === 0) {
      const orderData = response.data.data;
      
      return {
        // 主单信息
        orderInfo: {
          id: orderData.id,
          orderNo: orderData.orderNo,
          customerName: orderData.customerName,
          phoneNumber: orderData.phoneNumber,
          storageTime: formatTimestamp(orderData.storageTime),
          totalItems: orderData.totalItems,
          totalQuantity: orderData.totalQuantity,
          remainingQuantity: orderData.remainingQuantity,
          handler: orderData.handler,
          remark: orderData.remark
        },
        // 明细项
        items: orderData.items.map(item => ({
          id: item.id,
          orderNo: item.orderNo,
          productName: item.productName,
          productUnit: item.productUnit,
          productSpec: item.productSpec,
          quantity: item.quantity,
          remainingQty: item.remainingQty,
          storageLocation: item.storageLocation,
          expireTime: formatTimestamp(item.expireTime),
          // 计算状态
          status: item.remainingQty === 0 ? '已取完' : 
                 item.remainingQty === item.quantity ? '已存' : '部分取出'
        }))
      };
    }
    return null;
  } catch (error) {
    console.error('获取存酒单详情失败', error);
    return null;
  }
}
```

## 4. 业务流程

### 4.1 存酒流程

1. 客户到店存酒，可能存一种或多种商品
2. 前端收集客户信息和商品信息
3. 前端根据商品数量，选择单商品存酒或批量存酒接口
4. 后端系统根据请求类型，创建相应的记录:
   - 单商品：创建一条`ProductStorage`记录
   - 多商品：创建一条`ProductStorageOrder`记录和多条关联的`ProductStorage`记录
5. 返回结果给前端，前端更新界面显示

### 4.2 取酒流程

1. 客户需要取用之前存放的酒水
2. 前端通过存酒列表或客户信息查询该客户的存酒记录
3. 选择要取出的商品及数量，提交取酒请求
4. 后端系统检查数量是否合法，并创建取酒记录
5. 同时更新存酒记录的剩余数量
6. 返回结果给前端，前端更新界面显示取酒成功和剩余数量

### 4.3 续存流程

1. 客户的存酒将要到期，但希望继续存放
2. 前端调用`/api/product-storage/operate`接口，设置操作类型为"extend"
3. 提供新的到期时间和备注信息
4. 后端系统更新存酒记录的到期时间，并记录操作历史
5. 返回结果给前端，前端更新界面显示新的到期时间

### 4.4 报废流程

1. 因为过期或其他原因，需要报废某些存酒记录
2. 前端调用`/api/product-storage/operate`接口，设置操作类型为"discard"
3. 提供报废原因和备注信息
4. 后端系统更新存酒记录的状态和剩余数量（设为0），并记录操作历史
5. 返回结果给前端，前端更新界面显示报废状态

## 5. 数据模型详细字段说明

### 5.1 ProductStorageOrder（存酒单）

| 字段名 | 类型 | 说明 | 示例值 |
| --- | --- | --- | --- |
| id | string | 主键ID | "7a2d9a6957884bcd846d6dcf95d2e6a0" |
| order_no | string | 存酒单号 | "PS94YTNnVUk1744595036" |
| venue_id | string | 门店ID | "94YTNnVUk" |
| customer_id | string | 客户ID | "C_18500851553" |
| customer_name | string | 客户姓名 | "vincentyang" |
| phone_number | string | 电话号码 | "18500851553" |
| storage_time | int64 | 存入时间(Unix时间戳) | 1744595036 |
| total_items | int | 商品总项数 | 2 |
| total_quantity | int | 商品总数量 | 2 |
| remaining_quantity | int | 剩余总数量 | 2 |
| remark | string | 备注 | "客户生日存酒" |
| handler | string | 处理人 | "meGmGjhsI" |
| ctime | int64 | 创建时间(Unix时间戳) | 1744595036 |
| utime | int64 | 更新时间(Unix时间戳) | 1744595036 |
| state | int | 状态(0-正常) | 0 |
| version | int | 版本号 | 0 |

### 5.2 ProductStorage（存酒明细）

| 字段名 | 类型 | 说明 | 示例值 |
| --- | --- | --- | --- |
| id | string | 主键ID | "9b2eabe65fb34c9ea8f50169f3dfd9c8" |
| order_no | string | 明细单号 | "PS94YTNnVUk1744595036-002" |
| parent_order_no | string | 父订单号 | "PS94YTNnVUk1744595036" |
| venue_id | string | 门店ID | "94YTNnVUk" |
| customer_id | string | 客户ID | "C_18500851553" |
| customer_name | string | 客户姓名 | "vincentyang" |
| phone_number | string | 电话号码 | "18500851553" |
| product_id | string | 产品ID | "61a18bf5c7934f91acbcb933582afa3d" |
| product_name | string | 产品名称 | "脉动" |
| product_unit | string | 产品单位 | "瓶" |
| product_spec | string | 产品规格 | "" |
| quantity | int | 存入数量 | 1 |
| remaining_qty | int | 剩余数量 | 1 |
| storage_location | string | 存放位置 | "main" |
| storage_time | int64 | 存入时间(Unix时间戳) | 1744595036 |
| expire_time | int64 | 到期时间(Unix时间戳) | 1776131036 |
| remark | string | 备注 | "" |
| handler | string | 处理人 | "meGmGjhsI" |
| is_batch | int | 是否批量操作(0-否,1-是) | 0 |
| batch_time | int64 | 批量操作时间 | 0 |
| ctime | int64 | 创建时间(Unix时间戳) | 1744595036 |
| utime | int64 | 更新时间(Unix时间戳) | 1744595036 |
| state | int | 状态(0-正常) | 0 |
| version | int | 版本号 | 0 |

## 6. 请求/响应示例

### 6.1 创建存酒单（批量添加多个商品）

**请求：**

```json
POST /api/product-storage/add
{
  "venueId": "94YTNnVUk",
  "customerName": "vincentyang",
  "phoneNumber": "18500851553",
  "storageTime": 1744595036,
  "remark": "客户生日存酒",
  "handler": "meGmGjhsI",
  "items": [
    {
      "productId": "2333d1138ee741119a4b54e903f754d6",
      "productName": "红牛",
      "productUnit": "瓶",
      "productSpec": "",
      "quantity": 1,
      "storageLocation": "main",
      "expireTime": 1776131036,
      "remark": ""
    },
    {
      "productId": "61a18bf5c7934f91acbcb933582afa3d",
      "productName": "脉动",
      "productUnit": "瓶",
      "productSpec": "",
      "quantity": 1,
      "storageLocation": "main",
      "expireTime": 1776131036,
      "remark": ""
    }
  ]
}
```

**响应：**

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": "7a2d9a6957884bcd846d6dcf95d2e6a0",
    "orderNo": "PS94YTNnVUk1744595036",
    "venueId": "94YTNnVUk",
    "customerId": "C_18500851553",
    "customerName": "vincentyang",
    "phoneNumber": "18500851553",
    "storageTime": 1744595036,
    "totalItems": 2,
    "totalQuantity": 2,
    "remainingQuantity": 2,
    "remark": "客户生日存酒",
    "handler": "meGmGjhsI",
    "ctime": 1744595036,
    "utime": 1744595036,
    "state": 0,
    "version": 0,
    "items": [
      {
        "id": "b2b67bca67a044d9b396aa41dbbd7559",
        "orderNo": "PS94YTNnVUk1744595036-001",
        "parentOrderNo": "PS94YTNnVUk1744595036",
        "venueId": "94YTNnVUk",
        "customerId": "C_18500851553",
        "customerName": "vincentyang",
        "phoneNumber": "18500851553",
        "productId": "2333d1138ee741119a4b54e903f754d6",
        "productName": "红牛",
        "productUnit": "瓶",
        "productSpec": "",
        "quantity": 1,
        "remainingQty": 1,
        "storageLocation": "main",
        "storageTime": 1744595036,
        "expireTime": 1776131036,
        "remark": "",
        "handler": "meGmGjhsI",
        "isBatch": 0,
        "batchTime": 0,
        "ctime": 1744595036,
        "utime": 1744595036,
        "state": 0,
        "version": 0
      },
      {
        "id": "9b2eabe65fb34c9ea8f50169f3dfd9c8",
        "orderNo": "PS94YTNnVUk1744595036-002",
        "parentOrderNo": "PS94YTNnVUk1744595036",
        "venueId": "94YTNnVUk",
        "customerId": "C_18500851553",
        "customerName": "vincentyang",
        "phoneNumber": "18500851553",
        "productId": "61a18bf5c7934f91acbcb933582afa3d",
        "productName": "脉动",
        "productUnit": "瓶",
        "productSpec": "",
        "quantity": 1,
        "remainingQty": 1,
        "storageLocation": "main",
        "storageTime": 1744595036,
        "expireTime": 1776131036,
        "remark": "",
        "handler": "meGmGjhsI",
        "isBatch": 0,
        "batchTime": 0,
        "ctime": 1744595036,
        "utime": 1744595036,
        "state": 0,
        "version": 0
      }
    ]
  },
  "requestID": "b6830fbc-654f-40cf-9d6d-8a1476cf726b",
  "serverTime": 1744595559
}
```

### 6.2 查询存酒列表

**请求：**

```json
POST /api/product-storage/query
{
  "venueId": "94YTNnVUk",
  "searchText": "vincentyang",
  "onlyRemaining": true,
  "pageNum": 1,
  "pageSize": 10
}
```

**响应：**

```json
{
  "code": 0,
  "message": "success",
  "data": [
    {
      "id": "9b2eabe65fb34c9ea8f50169f3dfd9c8",
      "orderNo": "PS94YTNnVUk1744595036-002",
      "parentOrderNo": "PS94YTNnVUk1744595036",
      "venueId": "94YTNnVUk",
      "customerId": "C_18500851553",
      "customerName": "vincentyang",
      "phoneNumber": "18500851553",
      "productId": "61a18bf5c7934f91acbcb933582afa3d",
      "productName": "脉动",
    "productUnit": "瓶",
      "productSpec": "",
      "quantity": 1,
      "remainingQty": 1,
      "storageLocation": "main",
      "storageTime": 1744595036,
      "expireTime": 1776131036,
      "remark": "",
      "handler": "meGmGjhsI",
    "isBatch": 0,
    "batchTime": 0,
      "ctime": 1744595036,
      "utime": 1744595036,
    "state": 0,
      "version": 0
    },
    {
      "id": "b2b67bca67a044d9b396aa41dbbd7559",
      "orderNo": "PS94YTNnVUk1744595036-001",
      "parentOrderNo": "PS94YTNnVUk1744595036",
      "venueId": "94YTNnVUk",
      "customerId": "C_18500851553",
      "customerName": "vincentyang",
      "phoneNumber": "18500851553",
      "productId": "2333d1138ee741119a4b54e903f754d6",
      "productName": "红牛",
      "productUnit": "瓶",
      "productSpec": "",
      "quantity": 1,
      "remainingQty": 1,
      "storageLocation": "main",
      "storageTime": 1744595036,
      "expireTime": 1776131036,
      "remark": "",
      "handler": "meGmGjhsI",
      "isBatch": 0,
      "batchTime": 0,
      "ctime": 1744595036,
      "utime": 1744595036,
      "state": 0,
      "version": 0
    },
    {
      "id": "dc463766f0ab4b8aae7d64e48df93a4f",
      "orderNo": "PS94YTNnVUk1744594596-001",
      "parentOrderNo": "PS94YTNnVUk1744594596",
      "venueId": "94YTNnVUk",
      "customerId": "C_18500851553",
      "customerName": "vincentyang",
      "phoneNumber": "18500851553",
      "productId": "adb6c6b1ae19450a9d75d7d129863831",
      "productName": "皮蛋瘦肉粥",
      "productUnit": "瓶",
      "productSpec": "",
      "quantity": 1,
      "remainingQty": 1,
      "storageLocation": "main",
      "storageTime": 1744594596,
      "expireTime": 1776130596,
      "remark": "",
      "handler": "meGmGjhsI",
      "isBatch": 0,
      "batchTime": 0,
      "ctime": 1744594596,
      "utime": 1744594596,
      "state": 0,
      "version": 0
    }
  ],
  "requestID": "b6830fbc-654f-40cf-9d6d-8a1476cf726b",
  "serverTime": 1744595559
}
```

## 7. 错误码和注意事项

### 7.1 错误码

| 错误码 | 描述 | 处理建议 |
| --- | --- | --- |
| 1001 | 参数错误 | 检查请求参数是否完整、格式是否正确 |
| 1002 | 服务器忙 | 稍后重试或联系系统管理员 |
| 2001 | 商品存储记录不存在 | 检查所提供的存酒记录ID是否正确 |
| 2002 | 提取数量超过剩余数量 | 修改取酒数量，不能超过剩余数量 |
| 3001 | 操作失败 | 检查操作类型和参数，或联系系统管理员 |

### 7.2 前端开发注意事项

1. **时间处理**：
   - 后端使用Unix时间戳（秒），前端展示时需转换为可读格式
   - 前端提交时间时，需将日期转换为Unix时间戳

2. **存酒单与存酒商品关系**：
   - 列表页展示的是存酒商品记录，多个商品可能属于同一个存酒单
   - 存酒单号格式为`PS + 门店ID + 时间戳`，存酒商品单号在此基础上增加`-序号`
   - 通过商品单号的前缀部分可以判断商品所属的存酒单，例如`PS94YTNnVUk1744595036-001`和`PS94YTNnVUk1744595036-002`属于同一个存酒单
   - 批量存酒时，所有商品共享同一个存酒单号前缀，但有不同的序号后缀

3. **取酒操作**：
   - 取酒时必须指定具体的存酒商品记录ID，而不是存酒单ID
   - 取酒数量不能超过剩余数量
   - 取酒会自动更新剩余数量

4. **操作历史**：
   - 存酒商品详情页会显示该商品的完整操作历史（存、取、续存、报废等）
   - 使用`/api/product-storage/detail/{id}`接口获取操作历史

5. **数据刷新**：
   - 进行存取操作后，应当刷新列表数据以获取最新状态
   - 可以使用轮询或WebSocket实现数据实时更新 

## 8. 取酒流程详细说明

### 8.1 取酒页面流程设计

#### 8.1.1 搜索用户可取酒品流程

1. **输入客户信息**：
   - 通过手机号、会员卡号或姓名搜索客户信息
   - 系统根据客户信息查询该客户的所有存酒记录

2. **查询可取商品列表**：
   - 系统会查询该客户在当前门店的所有存酒单（可能有多个）
   - 根据存酒单获取所有存酒商品明细
   - 过滤出剩余数量大于0且未报废的商品（只显示可取的商品）
   - 如果商品已全部取完或已报废，不会显示在可取列表中

3. **显示结果**：
   - 显示该客户的基本信息（姓名、手机号）
   - 显示所有可取的商品列表，包括商品名称、规格、存入时间、剩余数量等信息

#### 8.1.2 取酒界面交互设计

**界面布局**：
- 左侧：**待选区**（商品选择区）
  - 显示客户所有可取商品列表
  - 每个商品显示名称、规格、单位、剩余数量、存入时间
  - 提供"添加到取酒单"按钮
  - 可选择取酒数量（不能超过剩余数量）

- 右侧：**取酒单区**（类似购物车概念）
  - 显示已选择要取出的商品列表
  - 可调整取酒数量或移除已选商品
  - 显示本次取酒的商品总数
  - 提供表单填写取酒相关信息（包括房间号、消费订单关联等）
  - "确认取酒"按钮提交取酒请求

**交互流程**：
1. 用户搜索并查询到可取商品
2. 从左侧待选区选择商品，点击"添加"按钮将商品加入右侧取酒单
3. 在右侧取酒单区可以调整数量或移除商品
4. 填写取酒相关信息（关联房间、订单等）
5. 点击"确认取酒"按钮提交

### 8.2 接口设计与模型检查

#### 8.2.1 查询客户可取商品接口

当前系统中用于查询客户可取商品的接口主要是：
- `/api/product-storage/query` - 可通过客户ID、手机号等筛选条件查询存酒记录
- 查询参数可以设置 `onlyRemaining: true` 来只获取有剩余数量的商品

**接口存在的问题与补充建议**：
1. 缺少专门的客户可取商品查询接口，建议补充：
   - **新接口**: `/api/product-storage/query-by-customer` 
   - **用途**: 专门用于取酒场景，根据客户信息直接查询所有可取商品
   - **请求参数**:
```json
     {
       "venueId": "门店ID",
       "customerId": "客户ID",  // 客户ID和手机号二选一
       "phoneNumber": "手机号", 
       "onlyValid": true        // 是否只返回有效的（剩余数量>0且未报废）
     }
     ```
   - **返回数据**: 客户信息及其所有可取商品列表

#### 8.2.2 批量取酒接口优化

现有的 `/api/product-withdraw/batch-add` 接口需要确保支持以下功能：

1. **一次性提交多个存酒记录的取酒请求**
   - 支持不同存酒单的商品一起取出
   - 每个商品可以指定不同的取酒数量

2. **自动生成取酒单**
   - 创建一个主取酒单及多个取酒明细记录
   - 建立与原存酒记录的关联关系

3. **数据校验**
   - 验证每个商品的取酒数量不超过其剩余数量
   - 验证商品是否可取（未报废、未过期等）

#### 8.2.3 接口调用示例

**查询客户可取商品**：
```javascript
// 查询客户可取商品
async function fetchCustomerWithdrawableProducts(params) {
  try {
    const response = await axios.post('/api/product-storage/query', {
      venueId: currentVenueId,
      customerId: params.customerId,
      phoneNumber: params.phoneNumber,
      onlyRemaining: true, // 只返回有剩余数量的记录
      state: 0 // 正常状态（非报废）
    });
    
    if (response.data.code === 0) {
      return {
        customerInfo: {
          id: response.data.data.customerId,
          name: response.data.data.customerName,
          phone: response.data.data.phoneNumber
        },
        productList: response.data.data.list.filter(item => item.remainingQty > 0)
      };
    }
    return { customerInfo: null, productList: [] };
  } catch (error) {
    console.error('获取客户可取商品失败', error);
    return { customerInfo: null, productList: [] };
  }
}
```

**批量取酒**：
```javascript
// 批量取酒
async function batchWithdrawProducts(formData) {
  try {
    const payload = {
      venueId: currentVenueId,
      customerId: formData.customerId,
      customerName: formData.customerName,
      phoneNumber: formData.phoneNumber,
      roomId: formData.roomId || '',
      roomName: formData.roomName || '',
      orderNumber: formData.orderNumber || '', // 关联消费订单号
      withdrawTime: Math.floor(new Date().getTime() / 1000),
      remark: formData.remark,
      handler: currentUser.id,
      // 多个取酒项
      items: formData.items.map(item => ({
        storageId: item.storageId, // 存酒记录ID
        storageOrderNo: item.storageOrderNo, // 存酒明细单号
        productId: item.productId,
        productName: item.productName,
        quantity: item.quantity // 取酒数量
      }))
    };
    
    const response = await axios.post('/api/product-withdraw/batch-add', payload);
    return response.data.code === 0;
  } catch (error) {
    console.error('批量取酒失败', error);
    return false;
  }
}
```

### 8.3 前端实现建议

1. **客户搜索组件**:
   - 支持通过手机号、会员卡号、姓名等多种方式搜索
   - 支持模糊匹配

2. **商品选择交互**:
   - 左侧待选区采用卡片式布局，清晰展示每个商品信息
   - 提供数量选择器，限制最大数量不超过剩余数量
   - 商品可分类显示，如按存入时间、商品类型等

3. **取酒单区（购物车）**:
   - 动态计算总数量
   - 支持批量删除
   - 提供清空功能

4. **提交确认机制**:
   - 提交前显示确认对话框，列出所有将要取出的商品
   - 提交成功后显示取酒凭据，支持打印

5. **数据刷新策略**:
   - 取酒成功后自动刷新剩余可取商品列表
   - 对于长时间打开的页面，定时检查商品是否仍可取

## 9. 业务流程图

```

## 10. 接口优化建议

根据对取酒业务场景的深入分析，我们发现现有的接口在某些使用场景下存在一些不足。以下是针对取酒场景的接口优化建议，这些接口将使前端开发更加便捷，并提高用户体验。

### 10.1 新增接口：根据客户信息查询可取商品

#### 10.1.1 接口定义

| 接口路径 | 方法 | 功能描述 | 前端使用场景 |
| --- | --- | --- | --- |
| `/api/product-withdraw/query-withdrawable-items` | POST | 根据客户信息查询可取商品列表 | 取酒页面搜索客户可取商品时调用 |

**请求参数**：

```json
{
  "venueId": "94YTNnVUk",            // 必填, 门店ID
  "searchType": "phone",             // 必填, 搜索类型: phone(手机号)/name(姓名)/id(客户ID)/card(会员卡号)
  "searchValue": "18500851553",      // 必填, 搜索值
  "fuzzyMatch": true,                // 可选, 是否模糊匹配，默认true
  "includeExpiringSoon": true,       // 可选, 是否包含即将到期的商品，默认true
  "expireDays": 30                   // 可选, 即将到期的天数定义，默认30天
}
```

**响应数据**：

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "customerInfo": {
      "id": "C_18500851553",
      "name": "vincentyang",
      "phoneNumber": "18500851553",
      "memberCardNo": "M293847"
    },
    "withdrawableItems": [
      {
        "id": "9b2eabe65fb34c9ea8f50169f3dfd9c8",
        "orderNo": "PS94YTNnVUk1744595036-002",
        "parentOrderNo": "PS94YTNnVUk1744595036",
        "productId": "61a18bf5c7934f91acbcb933582afa3d",
        "productName": "脉动",
        "productUnit": "瓶",
        "productSpec": "",
    "quantity": 1,
        "remainingQty": 1,
        "storageLocation": "main",
        "storageTime": 1744595036,
        "expireTime": 1776131036,
        "expiringStatus": "normal",  // normal:正常, soon:即将到期, expired:已过期
        "daysToExpire": 365          // 距离过期还有多少天，如果已过期则为负数
      },
      {
        "id": "b2b67bca67a044d9b396aa41dbbd7559",
        "orderNo": "PS94YTNnVUk1744595036-001",
        "parentOrderNo": "PS94YTNnVUk1744595036",
        "productId": "2333d1138ee741119a4b54e903f754d6",
        "productName": "红牛",
        "productUnit": "瓶",
        "productSpec": "",
        "quantity": 1,
        "remainingQty": 1,
        "storageLocation": "main",
        "storageTime": 1744595036,
        "expireTime": 1776131036,
        "expiringStatus": "normal",
        "daysToExpire": 365
      }
    ],
    "storageOrders": [
      {
        "orderNo": "PS94YTNnVUk1744595036",
        "storageTime": 1744595036,
        "totalItems": 2,
        "totalQuantity": 2,
        "remainingQuantity": 2
      }
    ]
  },
  "requestID": "b6830fbc-654f-40cf-9d6d-8a1476cf726b",
  "serverTime": 1744595559
}
```

#### 10.1.2 接口说明

1. **功能优势**：
   - 一次性返回客户所有可取商品，避免多次请求
   - 已过滤掉不可取商品（剩余数量为0或已报废）
   - 包含过期状态信息，便于前端展示

2. **过期状态计算规则**：
   - `normal`: 正常状态，距离过期时间超过设定的预警天数（默认30天）
   - `soon`: 即将到期，距离过期时间小于预警天数但尚未过期
   - `expired`: 已过期，当前时间已超过过期时间

3. **使用场景**：
   - 取酒页面中，当客户通过各种方式（手机号、姓名等）搜索时使用
   - 返回结果可直接用于构建取酒界面的左侧待选区

### 10.2 新增接口：批量验证取酒数量

#### 10.2.1 接口定义

| 接口路径 | 方法 | 功能描述 | 前端使用场景 |
| --- | --- | --- | --- |
| `/api/product-withdraw/validate-quantities` | POST | 批量验证多个商品的取酒数量是否合法 | 取酒时验证用户选择的商品数量 |

**请求参数**：

```json
{
  "items": [
    {
      "storageId": "9b2eabe65fb34c9ea8f50169f3dfd9c8",
      "quantity": 1
    },
    {
      "storageId": "b2b67bca67a044d9b396aa41dbbd7559",
      "quantity": 2
    }
  ]
}
```

**响应数据**：

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "valid": false,
    "invalidItems": [
      {
        "storageId": "b2b67bca67a044d9b396aa41dbbd7559",
        "requestedQuantity": 2,
        "availableQuantity": 1,
        "reason": "超出可用数量"
      }
    ]
  },
  "requestID": "b6830fbc-654f-40cf-9d6d-8a1476cf726b",
  "serverTime": 1744595559
}
```

#### 10.2.2 接口说明

1. **功能优势**：
   - 在用户提交前提供数量验证，避免提交后才发现错误
   - 批量验证多个商品，减少请求次数
   - 详细返回无效项目的原因，便于前端展示错误信息

2. **验证规则**：
   - 检查商品是否存在
   - 检查商品是否已报废
   - 检查请求数量是否小于或等于剩余数量
   - 检查商品是否已过期

3. **使用场景**：
   - 用户在取酒单中调整数量时实时验证
   - 提交取酒请求前的最终验证

### 10.3 取酒单查询优化

#### 10.3.1 接口定义

| 接口路径 | 方法 | 功能描述 | 前端使用场景 |
| --- | --- | --- | --- |
| `/api/product-withdraw/order/{orderNo}` | GET | 获取完整取酒单信息（包括主单信息及所有关联商品） | 取酒单详情页面加载数据时调用 |

**响应数据**：

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": "f8b79345e7ce4e51b16b8d9fdd11b2f3",
    "orderNo": "PW94YTNnVUk1744596240",
    "venueId": "94YTNnVUk",
    "customerId": "C_18500851553",
    "customerName": "vincentyang",
    "phoneNumber": "18500851553",
    "orderNumber": "ORD123456",
    "withdrawTime": 1744596240,
    "roomId": "R001",
    "roomName": "VIP包房1",
    "totalItems": 2,
    "totalQuantity": 2,
    "remark": "客户取酒",
    "handler": "meGmGjhsI",
    "ctime": 1744596240,
    "utime": 1744596240,
    "state": 0,
    "version": 0,
    "items": [
      {
        "id": "d1b1f85e31684107a1b3a8b2e2a1d3f5",
        "orderNo": "PW94YTNnVUk1744596240-001",
        "parentOrderNo": "PW94YTNnVUk1744596240",
        "storageId": "9b2eabe65fb34c9ea8f50169f3dfd9c8",
        "storageOrderNo": "PS94YTNnVUk1744595036-002",
        "productId": "61a18bf5c7934f91acbcb933582afa3d",
        "productName": "脉动",
        "productUnit": "瓶",
        "productSpec": "",
        "quantity": 1,
        "withdrawTime": 1744596240,
        "storageInfo": {
          "orderNo": "PS94YTNnVUk1744595036-002",
          "storageTime": 1744595036,
          "originalQuantity": 1,
          "remainingQuantity": 0
        }
      },
      {
        "id": "e2c2f96f42795218b2c4b9c3f3b2e4g6",
        "orderNo": "PW94YTNnVUk1744596240-002",
        "parentOrderNo": "PW94YTNnVUk1744596240",
        "storageId": "b2b67bca67a044d9b396aa41dbbd7559",
        "storageOrderNo": "PS94YTNnVUk1744595036-001",
        "productId": "2333d1138ee741119a4b54e903f754d6",
        "productName": "红牛",
        "productUnit": "瓶",
        "productSpec": "",
        "quantity": 1,
        "withdrawTime": 1744596240,
        "storageInfo": {
          "orderNo": "PS94YTNnVUk1744595036-001",
          "storageTime": 1744595036,
          "originalQuantity": 1,
          "remainingQuantity": 0
        }
      }
    ]
  },
  "requestID": "b6830fbc-654f-40cf-9d6d-8a1476cf726b",
  "serverTime": 1744595559
}
```

#### 10.3.2 接口说明

1. **功能优势**：
   - 一次性返回取酒单的所有信息，包括明细和相关存酒信息
   - 在每个取酒明细中包含对应的存酒信息，方便追溯

2. **优化点**：
   - 增加关联的存酒信息，提供原始存酒数量和当前剩余数量
   - 包含完整的房间信息和订单关联信息

3. **使用场景**：
   - 查看取酒单详情
   - 打印取酒凭据
   - 取酒记录追溯

### 10.4 前端功能实现建议

基于以上接口优化，前端开发取酒功能时可实现以下优化：

1. **实时状态更新**：
   - 使用新增的验证接口在用户调整取酒数量时实时验证
   - 显示商品过期状态，对即将过期的商品进行醒目提示

2. **分组展示**：
   - 根据存酒单号对商品进行分组展示
   - 将即将到期的商品放在前面展示

3. **批量操作优化**：
   - 支持一键添加所有可取商品
   - 支持按存酒单批量添加

4. **取酒单打印**：
   - 利用新的取酒单查询接口，生成包含完整信息的打印凭据
   - 包含存酒信息和取酒信息的对照

5. **库存实时检查**：
   - 在提交取酒请求前进行最终数量验证
   - 处理多人同时操作可能导致的数据冲突
