sequenceDiagram
    participant Frontend as 前端 (UI)
    participant Backend as 后端 API
    participant Database as 数据库

    %% 1. 查找客户并显示可取商品 %%
    Frontend->>Backend: POST /api/product-storage/query-withdrawable (查询客户可取商品)
    Note right of Frontend: 发送客户搜索信息 (手机号/姓名/ID等)
    Backend->>Database: 查询 product_storage 表
    Note right of Backend: 条件: venueId, customerInfo, remaining_qty > 0, state = normal
    Database-->>Backend: 返回符合条件的存酒记录
    Backend-->>Frontend: 返回 WithdrawableItemsVO (客户信息, 可取商品列表, 关联存酒单信息)
    Note left of Backend: 包含商品名称, 规格, 剩余数量, 过期状态等

    %% 2. 用户选择商品并输入取酒信息 %%
    Frontend->>Frontend: 用户在界面选择要取的商品和数量
    Note right of Frontend: 输入房间信息, 关联订单号 (可选), 备注等

    %% 3. (可选) 提交前验证数量 %%
    opt 数量验证
        Frontend->>Backend: POST /api/product-withdraw/validate-quantities
        Note right of Frontend: 发送待验证商品列表 (storageId, quantity)
        Backend->>Database: 查询 product_storage 表
        Note right of Backend: 检查每个 storageId 的 state 和 remaining_qty
        Database-->>Backend: 返回商品状态和剩余数量
        Backend-->>Frontend: 返回 ValidateResultVO (验证结果, 无效项及原因)
        alt 验证失败
            Frontend->>Frontend: 显示错误信息给用户
        end
    end

    %% 4. 确认取酒并提交 %%
    Frontend->>Backend: POST /api/product-withdraw/order (创建取酒单)
    Note right of Frontend: 发送 AddProductWithdrawOrderReqDto (包含取酒单信息和明细列表)
    Backend->>Database: 开始事务 (BEGIN TRANSACTION)
    Backend->>Database: INSERT INTO product_withdraw_order (创建取酒单主记录)
    loop 遍历每个取酒明细项
        Backend->>Database: SELECT FROM product_storage (查询对应存酒记录, 再次校验状态和数量)
        alt 校验失败 (状态异常或数量不足)
            Backend->>Database: 回滚事务 (ROLLBACK)
            Backend-->>Frontend: 返回错误信息
            Note over Backend,Frontend: 终止流程
        end
        Backend->>Database: INSERT INTO product_withdraw (创建取酒明细记录)
        Backend->>Database: UPDATE product_storage (减少 remaining_qty, 更新 utime)
        Backend->>Database: 计算存酒单剩余总数量 (SUM(remaining_qty) WHERE parent_order_no = ?)
        Backend->>Database: UPDATE product_storage_order (更新主单 remaining_quantity, 更新 utime)
    end
    Backend->>Database: 提交事务 (COMMIT)
    Database-->>Backend: 事务成功
    Backend-->>Frontend: 返回 ProductWithdrawOrderVO (包含新创建的取酒单和明细)
    Note left of Backend: 操作成功
