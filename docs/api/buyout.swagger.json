{"swagger": "2.0", "info": {"title": "Buyout Price Plan API", "version": "1.0.0", "description": "买断价格方案相关接口"}, "basePath": "/", "tags": [{"name": "买断价格方案", "description": "买断价格方案相关操作"}], "paths": {"/api/buyout-price-plan/create": {"post": {"tags": ["买断价格方案"], "summary": "创建买断价格方案", "description": "创建买断价格方案", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "请求体", "required": true, "schema": {"$ref": "#/definitions/CreateBuyoutPricePlanReqDto"}}], "responses": {"200": {"description": "成功", "schema": {"$ref": "#/definitions/BuyoutPricePlanVO"}}, "400": {"description": "请求参数错误"}, "500": {"description": "服务器内部错误"}}}}, "/api/buyout-price-plan/update": {"post": {"tags": ["买断价格方案"], "summary": "更新买断价格方案", "description": "更新买断价格方案", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "请求体", "required": true, "schema": {"$ref": "#/definitions/UpdateBuyoutPricePlanReqDto"}}], "responses": {"200": {"description": "成功", "schema": {"$ref": "#/definitions/BuyoutPricePlanVO"}}, "400": {"description": "请求参数错误"}, "500": {"description": "服务器内部错误"}}}}, "/api/buyout-price-plan/delete": {"post": {"tags": ["买断价格方案"], "summary": "删除买断价格方案", "description": "删除买断价格方案", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "请求体", "required": true, "schema": {"$ref": "#/definitions/DeleteBuyoutPricePlanReqDto"}}], "responses": {"200": {"description": "成功"}, "400": {"description": "请求参数错误"}, "500": {"description": "服务器内部错误"}}}}, "/api/buyout-price-plan/get": {"post": {"tags": ["买断价格方案"], "summary": "查询单个买断价格方案", "description": "查询单个买断价格方案", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "请求体", "required": true, "schema": {"$ref": "#/definitions/GetBuyoutPricePlanReqDto"}}], "responses": {"200": {"description": "成功", "schema": {"$ref": "#/definitions/BuyoutPricePlanVO"}}, "400": {"description": "请求参数错误"}, "500": {"description": "服务器内部错误"}}}}, "/api/buyout-price-plan/list": {"post": {"tags": ["买断价格方案"], "summary": "列出买断价格方案（分页）", "description": "列出买断价格方案（分页）", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "请求体", "required": true, "schema": {"$ref": "#/definitions/ListBuyoutPricePlansReqDto"}}], "responses": {"200": {"description": "成功", "schema": {"$ref": "#/definitions/PageVO_BuyoutPricePlanVO"}}, "400": {"description": "请求参数错误"}, "500": {"description": "服务器内部错误"}}}}, "/api/buyout-price-plan/query": {"post": {"tags": ["买断价格方案"], "summary": "查询买断价格方案（不分页）", "description": "查询买断价格方案（不分页）", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "请求体", "required": true, "schema": {"$ref": "#/definitions/ListBuyoutPricePlansReqDto"}}], "responses": {"200": {"description": "成功", "schema": {"type": "array", "items": {"$ref": "#/definitions/BuyoutPricePlanVO"}}}, "400": {"description": "请求参数错误"}, "500": {"description": "服务器内部错误"}}}}, "/api/buyout-price-plan/enable": {"post": {"tags": ["买断价格方案"], "summary": "启用买断价格方案", "description": "启用买断价格方案", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "请求体", "required": true, "schema": {"$ref": "#/definitions/EnableBuyoutPricePlanReqDto"}}], "responses": {"200": {"description": "成功"}, "400": {"description": "请求参数错误"}, "500": {"description": "服务器内部错误"}}}}, "/api/buyout-price-plan/disable": {"post": {"tags": ["买断价格方案"], "summary": "禁用买断价格方案", "description": "禁用买断价格方案", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "请求体", "required": true, "schema": {"$ref": "#/definitions/DisableBuyoutPricePlanReqDto"}}], "responses": {"200": {"description": "成功"}, "400": {"description": "请求参数错误"}, "500": {"description": "服务器内部错误"}}}}, "/api/buyout-price-plan/calculate-price": {"post": {"tags": ["买断价格方案"], "summary": "计算价格", "description": "计算价格", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "请求体", "required": true, "schema": {"$ref": "#/definitions/CalculateBuyoutPriceReqDto"}}], "responses": {"200": {"description": "成功", "schema": {"$ref": "#/definitions/CalculatePriceOutput"}}, "400": {"description": "请求参数错误"}, "500": {"description": "服务器内部错误"}}}}}, "definitions": {"CreateBuyoutPricePlanReqDto": {"type": "object", "properties": {"id": {"type": "string", "description": "可选，如果不提供则自动生成"}, "venueId": {"type": "string", "description": "门店ID"}, "name": {"type": "string", "description": "方案名称"}, "roomTypeConfig": {"type": "array", "items": {"type": "string"}, "description": "房间类型ID数组"}, "timeConfig": {"type": "string", "description": "时间配置JSON字符串"}, "isEnabled": {"type": "boolean", "description": "是否启用"}, "channelConfig": {"type": "array", "items": {"type": "string"}, "description": "渠道ID数组"}, "isAreaSpecified": {"type": "boolean", "description": "是否指定投放区域"}, "areaConfig": {"type": "array", "items": {"type": "string"}, "description": "区域ID数组"}, "productSelection": {"type": "string", "description": "产品选择JSON字符串"}, "giftPlan": {"type": "string", "description": "消费赠券"}, "duration": {"type": "integer", "format": "int32", "description": "买断持续时长（分钟）"}, "advanceDisableDuration": {"type": "integer", "format": "int32", "description": "提前禁用时长（分钟）"}, "isExcessIncluded": {"type": "boolean", "description": "多余部分是否计入房费"}, "hasMinimumCharge": {"type": "boolean", "description": "是否有最低消费"}, "minimumCharge": {"type": "integer", "format": "int64", "description": "最低消费金额"}, "statisticsCategory": {"type": "string", "description": "统计分类"}, "planPic": {"type": "string", "description": "方案图片"}, "supportsPoints": {"type": "boolean", "description": "是否支持积分"}, "priceConfigList": {"type": "string", "description": "价格配置JSON字符串"}}, "required": ["venueId", "name", "roomTypeConfig", "timeConfig", "isEnabled", "channelConfig", "productSelection", "duration", "advanceDisableDuration", "isExcessIncluded"]}, "UpdateBuyoutPricePlanReqDto": {"type": "object", "properties": {"id": {"type": "string", "description": "方案ID"}, "name": {"type": "string", "description": "方案名称"}, "roomTypeConfig": {"type": "array", "items": {"type": "string"}, "description": "房间类型ID数组"}, "timeConfig": {"type": "string", "description": "时间配置JSON字符串"}, "isEnabled": {"type": "boolean", "description": "是否启用"}, "channelConfig": {"type": "array", "items": {"type": "string"}, "description": "渠道ID数组"}, "isAreaSpecified": {"type": "boolean", "description": "是否指定投放区域"}, "areaConfig": {"type": "array", "items": {"type": "string"}, "description": "区域ID数组"}, "productSelection": {"type": "string", "description": "产品选择JSON字符串"}, "giftPlan": {"type": "string", "description": "消费赠券"}, "duration": {"type": "integer", "format": "int32", "description": "买断持续时长（分钟）"}, "advanceDisableDuration": {"type": "integer", "format": "int32", "description": "提前禁用时长（分钟）"}, "isExcessIncluded": {"type": "boolean", "description": "多余部分是否计入房费"}, "hasMinimumCharge": {"type": "boolean", "description": "是否有最低消费"}, "minimumCharge": {"type": "integer", "format": "int64", "description": "最低消费金额"}, "statisticsCategory": {"type": "string", "description": "统计分类"}, "planPic": {"type": "string", "description": "方案图片"}, "supportsPoints": {"type": "boolean", "description": "是否支持积分"}, "priceConfigList": {"type": "string", "description": "价格配置JSON字符串"}}, "required": ["id"]}, "DeleteBuyoutPricePlanReqDto": {"type": "object", "properties": {"id": {"type": "string", "description": "方案ID"}}, "required": ["id"]}, "GetBuyoutPricePlanReqDto": {"type": "object", "properties": {"id": {"type": "string", "description": "方案ID"}}, "required": ["id"]}, "ListBuyoutPricePlansReqDto": {"type": "object", "properties": {"venueId": {"type": "string", "description": "门店ID，可选"}, "enabled": {"type": "boolean", "description": "是否启用，可选"}, "pageNum": {"type": "integer", "format": "int32", "description": "页码，可选"}, "pageSize": {"type": "integer", "format": "int32", "description": "每页大小，可选"}}}, "EnableBuyoutPricePlanReqDto": {"type": "object", "properties": {"id": {"type": "string", "description": "方案ID"}}, "required": ["id"]}, "DisableBuyoutPricePlanReqDto": {"type": "object", "properties": {"id": {"type": "string", "description": "方案ID"}}, "required": ["id"]}, "CalculateBuyoutPriceReqDto": {"type": "object", "properties": {"id": {"type": "string", "description": "方案ID"}, "checkTime": {"type": "integer", "format": "int64", "description": "检查时间戳"}, "areaId": {"type": "string", "description": "区域ID"}, "holidayId": {"type": "string", "description": "节假日ID"}, "priceType": {"type": "string", "description": "价格类型"}}, "required": ["id", "checkTime"]}, "BuyoutPricePlanVO": {"type": "object", "properties": {"id": {"type": "string"}, "venueId": {"type": "string"}, "name": {"type": "string"}, "roomTypeConfig": {"$ref": "#/definitions/RoomTypeConfig"}, "timeConfig": {"$ref": "#/definitions/TimeConfig"}, "isEnabled": {"type": "boolean"}, "channelConfig": {"$ref": "#/definitions/ChannelConfig"}, "areaConfig": {"$ref": "#/definitions/AreaConfig"}, "productSelection": {"$ref": "#/definitions/ProductSelection"}, "giftPlan": {"type": "string"}, "duration": {"type": "integer", "format": "int32"}, "advanceDisableDuration": {"type": "integer", "format": "int32"}, "isExcessIncluded": {"type": "boolean"}, "hasMinimumCharge": {"type": "boolean"}, "minimumCharge": {"type": "integer", "format": "int64"}, "statisticsCategory": {"type": "string"}, "planPic": {"type": "string"}, "supportsPoints": {"type": "boolean"}, "priceConfigList": {"$ref": "#/definitions/PriceConfigList"}, "createTime": {"type": "integer", "format": "int64"}, "updateTime": {"type": "integer", "format": "int64"}, "state": {"type": "integer", "format": "int32"}, "version": {"type": "integer", "format": "int32"}}}, "CalculatePriceOutput": {"type": "object", "properties": {"price": {"type": "integer", "format": "int64", "description": "价格，单位：分"}, "currency": {"type": "string", "description": "货币类型，例如：CNY"}, "description": {"type": "string", "description": "价格描述"}}}, "RoomTypeConfig": {"type": "object", "properties": {"roomTypes": {"type": "array", "items": {"$ref": "#/definitions/RoomType"}}}}, "RoomType": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}}}, "TimeConfig": {"type": "object", "properties": {"timeType": {"type": "string"}, "weekdayConfig": {"$ref": "#/definitions/WeekdayTimeConfig"}, "dateConfig": {"$ref": "#/definitions/DateTimeConfig"}}}, "WeekdayTimeConfig": {"type": "object", "properties": {"weeks": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "hourMinuteStart": {"type": "string"}, "hourMinuteEnd": {"type": "string"}}}, "DateTimeConfig": {"type": "object", "properties": {"dayStart": {"type": "string"}, "dayEnd": {"type": "string"}, "hourMinuteStart": {"type": "string"}, "hourMinuteEnd": {"type": "string"}}}, "ChannelConfig": {"type": "object", "properties": {"channels": {"type": "array", "items": {"$ref": "#/definitions/Channel"}}}}, "Channel": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}}}, "AreaConfig": {"type": "object", "properties": {"areas": {"type": "array", "items": {"$ref": "#/definitions/Area"}}}}, "Area": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}}}, "ProductSelection": {"type": "object", "properties": {"products": {"type": "array", "items": {"$ref": "#/definitions/Product"}}}}, "Product": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "category": {"type": "string"}, "price": {"type": "integer", "format": "int64"}, "quantity": {"type": "integer", "format": "int32"}}}, "PriceConfigList": {"type": "array", "items": {"$ref": "#/definitions/PriceSettingItem"}}, "PriceSettingItem": {"type": "object", "properties": {"name": {"type": "string"}, "type": {"type": "string"}, "price": {"type": "integer", "format": "int64"}, "holidayPrice": {"type": "array", "items": {"$ref": "#/definitions/HolidayPrice"}}, "areaPrice": {"type": "array", "items": {"$ref": "#/definitions/AreaPrice"}}}}, "HolidayPrice": {"type": "object", "properties": {"holidayId": {"type": "string"}, "price": {"type": "integer", "format": "int64"}, "areaPrice": {"type": "array", "items": {"$ref": "#/definitions/AreaPrice"}}}}, "AreaPrice": {"type": "object", "properties": {"areaId": {"type": "string"}, "price": {"type": "integer", "format": "int64"}}}, "PageVO_BuyoutPricePlanVO": {"type": "object", "properties": {"total": {"type": "integer", "format": "int64"}, "pageNum": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/definitions/BuyoutPricePlanVO"}}}}}}