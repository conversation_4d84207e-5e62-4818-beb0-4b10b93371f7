package main

import (
	"encoding/json"
	"os"
	"time"
	"voderpltvv/config"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/model"
	"voderpltvv/util"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

type MemberData struct {
	CardNumber       string `json:"card_number"`
	Status           string `json:"status"`
	Phone            string `json:"phone"`
	Name             string `json:"name"`
	Gender           string `json:"gender"`
	Birthday         int64  `json:"birthday"`
	PrincipalBalance int64  `json:"principalBalance"`
	BonusBalance     int64  `json:"bonusBalance"`
	Source           string `json:"source"`
	Ctime            int64  `json:"ctime"`
	Utime            int64  `json:"utime"`
	Balance          int64  `json:"balance"`
}

func initConfig(cfgFile string) {
	c := config.Config{
		Name: cfgFile,
	}
	if err := c.InitConfig(); err != nil {
		panic(err)
	}
	c.InitLog()
	logrus.Info("载入服务配置文件: ", cfgFile)
}

func main() {
	// 初始化配置
	cfgFile := "../../conf/local_config.yaml"
	if len(os.Args) > 1 {
		cfgFile = os.Args[1]
	}
	initConfig(cfgFile)

	// 初始化数据库
	model.InitDB()
	defer model.DBMaster.DBMasterClose()
	defer model.DBSlave.DBSlaveClose()
	logrus.Info("数据库连接成功")

	// 清空相关表
	if err := model.DBMaster.Self.Transaction(func(tx *gorm.DB) error {
		// 先删除关联表数据
		if err := tx.Exec("DELETE FROM venue_and_member").Error; err != nil {
			return err
		}
		// 再删除会员表数据
		if err := tx.Exec("DELETE FROM member").Error; err != nil {
			return err
		}
		return nil
	}); err != nil {
		panic(err)
	}
	logrus.Info("表数据清空完成")

	// 读取JSON文件
	data, err := os.ReadFile("member_data.json")
	if err != nil {
		panic(err)
	}

	// 尝试先解析为map来查看具体数据类型
	var rawData []map[string]interface{}
	if err := json.Unmarshal(data, &rawData); err != nil {
		logrus.Errorf("解析为map失败: %v", err)
		panic(err)
	}

	var memberDataList []MemberData
	if err := json.Unmarshal(data, &memberDataList); err != nil {
		// 打印详细错误信息
		logrus.Errorf("JSON解析失败: %v", err)
		// 尝试解析单个对象
		var singleMember MemberData
		if err := json.Unmarshal(data, &singleMember); err != nil {
			logrus.Errorf("尝试解析单个对象也失败: %v", err)
		} else {
			logrus.Infof("成功解析单个对象: %+v", singleMember)
		}
		panic(err)
	}

	logrus.Infof("成功解析 %d 条会员数据", len(memberDataList))

	// 遍历并打印每条数据
	for i, memberData := range memberDataList {
		logrus.Infof("第 %d 条数据: %+v", i+1, memberData)
		// 创建会员记录
		memberId := util.GetUUID()

		member := &po.Member{
			Id:               &memberId,
			Name:             &memberData.Name,
			Phone:            &memberData.Phone,
			CardNumber:       &memberData.CardNumber,
			Balance:          &memberData.Balance,
			PrincipalBalance: &memberData.PrincipalBalance,
			BonusBalance:     &memberData.BonusBalance,
			Birthday:         &memberData.Birthday,
			Gender:           &memberData.Gender,
			Ctime:            &memberData.Ctime,
			Utime:            &memberData.Utime,
			Source:           &memberData.Source,
			Status:           &memberData.Status,
		}

		// 开启事务
		err = model.DBMaster.Self.Transaction(func(tx *gorm.DB) error {
			// 插入会员数据
			if err := tx.Model(&po.Member{}).Create(member).Error; err != nil {
				return err
			}

			// 创建门店会员关联
			venueId := "105497"
			relationId := util.GetUUID()
			now := time.Now().Unix()
			state := 0
			version := 0

			// 中间表数据更新
			relation := &po.VenueAndMember{
				Id:       &relationId,
				VenueId:  &venueId,
				MemberId: &memberId,
				Ctime:    &now,
				Utime:    &now,
				State:    &state,
				Version:  &version,
			}

			if err := tx.Model(&po.VenueAndMember{}).Create(relation).Error; err != nil {
				return err
			}

			return nil
		})

		if err != nil {
			panic(err)
		}
	}

	logrus.Info("数据导入成功!")
}
