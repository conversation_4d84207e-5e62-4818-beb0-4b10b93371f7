package cmd

import (
	"encoding/json"

	"voderpltvv/util/version"

	"github.com/sirupsen/logrus"
	"github.com/spf13/cobra"
)

var versionCmd = &cobra.Command{
	Use:   "version",
	Short: "Print the version info of server",
	Long:  "Print the version info of server",
	Run: func(cmd *cobra.Command, args []string) {
		printVersion()
	},
}

func printVersion() {
	info := version.Get()
	infoj, err := json.MarshalIndent(&info, "", " ") // 加一点缩进
	logrus.Info("infoj: ", infoj)
	if err != nil {
		logrus.Warnf("遇到了错误: %v\n", err)
	}
	logrus.Warnf(string(infoj))
}
